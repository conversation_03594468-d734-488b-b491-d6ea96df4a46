<?php

namespace App\Http\Controllers\Client;

use App\Http\Controllers\Controller;
use App\Models\Reservation;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;

class ReservationController extends Controller
{
    public function index()
    {
        $reservations = Reservation::with(['stage.lieu.ville', 'typeStage'])
            ->where('user_id', Auth::id())
            ->orderBy('date_reservation', 'desc')
            ->get();

        return Inertia::render('Client/Reservations', [
            'reservations' => $reservations
        ]);
    }

    public function stages()
    {
        $reservations = Reservation::with(['stage.lieu.ville', 'typeStage'])
            ->where('user_id', Auth::id())
            ->orderBy('date_reservation', 'desc')
            ->get();

        return Inertia::render('Client/Stages', [
            'reservations' => $reservations
        ]);
    }

    public function tests()
    {
        $reservations = [];

        // Si le modèle ReservationTestPsycho existe, on récupère les réservations
        if (class_exists('App\Models\ReservationTestPsycho')) {
            $reservations = \App\Models\ReservationTestPsycho::with(['testPsycho.lieu.ville', 'typeTestPsycho'])
                ->where('user_id', Auth::id())
                ->orderBy('date_reservation', 'desc')
                ->get();
        }

        return Inertia::render('Client/Tests', [
            'reservations' => $reservations
        ]);
    }

    public function reservationTests()
    {
        $reservations = [];

        // Si le modèle ReservationTestPsycho existe, on récupère les réservations
        if (class_exists('App\Models\ReservationTestPsycho')) {
            $reservations = \App\Models\ReservationTestPsycho::with(['testPsycho.lieu.ville', 'typeTestPsycho'])
                ->where('user_id', Auth::id())
                ->orderBy('date_reservation', 'desc')
                ->get();
        }

        return Inertia::render('Client/ReservationTests', [
            'reservations' => $reservations
        ]);
    }
}
