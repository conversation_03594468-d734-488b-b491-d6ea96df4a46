<?php

namespace App\Services;

use App\Models\Reservation;
use App\Models\ReservationTestPsycho;
use App\Models\User;
use App\Notifications\ReservationConfirmedNotification;
use App\Notifications\TestReservationConfirmedNotification;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Notification;

class EmailNotificationService
{
    /**
     * Send notification for confirmed stage reservation
     *
     * @param Reservation $reservation
     * @return bool
     */
    public function sendStageReservationNotification(Reservation $reservation): bool
    {
        try {
            // Get admin email from config
            $adminEmail = config('mail.admin_email', env('ADMIN_EMAIL', '<EMAIL>'));

            // Create a temporary user object for the admin email
            // $adminUser = new User(['email' => $adminEmail]);
                    $admins = User::where('role', 'admin')->get();

        // Get admin email from config
        // $adminEmail = config('mail.admin_email', env('ADMIN_EMAIL', '<EMAIL>'));

        // // Create a temporary user object for the admin email
        // $adminUser = new User(['email' => $adminEmail]);

        Notification::send($admins, new ReservationConfirmedNotification($reservation));

            // Send notification
            // $adminUser->notify(new ReservationConfirmedNotification($reservation));

            Log::info('Stage reservation notification sent successfully', [
                'reservation_id' => $reservation->id,
                'admin_email' => $adminEmail,
                'user_email' => $reservation->user->email,
                'stage_date' => $reservation->stage->date_debut->format('Y-m-d'),
            ]);

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to send stage reservation notification', [
                'reservation_id' => $reservation->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return false;
        }
    }

    /**
     * Send notification for confirmed test reservation
     *
     * @param ReservationTestPsycho $reservation
     * @return bool
     */
    public function sendTestReservationNotification(ReservationTestPsycho $reservation): bool
    {
        try {
            // Get admin email from config
            $adminEmail = config('mail.admin_email', env('ADMIN_EMAIL', '<EMAIL>'));

            // Create a temporary user object for the admin email
            $adminUser = new User(['email' => $adminEmail]);

            // Send notification
            $adminUser->notify(new TestReservationConfirmedNotification($reservation));

            Log::info('Test reservation notification sent successfully', [
                'reservation_id' => $reservation->id,
                'admin_email' => $adminEmail,
                'user_email' => $reservation->user->email,
                'test_date' => $reservation->testPsycho->date_debut->format('Y-m-d'),
            ]);

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to send test reservation notification', [
                'reservation_id' => $reservation->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return false;
        }
    }

    /**
     * Send notification using direct email (alternative method)
     *
     * @param string $email
     * @param Reservation|ReservationTestPsycho $reservation
     * @param string $type ('stage' or 'test')
     * @return bool
     */
    public function sendDirectNotification(string $email, $reservation, string $type = 'stage'): bool
    {
        try {
            if ($type === 'test' && $reservation instanceof ReservationTestPsycho) {
                Notification::route('mail', $email)
                    ->notify(new TestReservationConfirmedNotification($reservation));
            } elseif ($type === 'stage' && $reservation instanceof Reservation) {
                Notification::route('mail', $email)
                    ->notify(new ReservationConfirmedNotification($reservation));
            } else {
                throw new \InvalidArgumentException('Invalid reservation type or object');
            }

            Log::info('Direct notification sent successfully', [
                'email' => $email,
                'type' => $type,
                'reservation_id' => $reservation->id,
            ]);

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to send direct notification', [
                'email' => $email,
                'type' => $type,
                'reservation_id' => $reservation->id ?? null,
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    /**
     * Check if reservation should trigger email notification
     *
     * @param Reservation|ReservationTestPsycho $reservation
     * @return bool
     */
    public function shouldSendNotification($reservation): bool
    {
        // Send notification only when status is 'confirmée'
        return $reservation->statut === 'confirmée';
    }
}
