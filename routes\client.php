<?php

use App\Http\Controllers\Client\DashboardController;
use App\Http\Controllers\Client\DocumentController;
use App\Http\Controllers\Client\ProfileController;
use App\Http\Controllers\Client\ReservationController;
use Illuminate\Support\Facades\Route;

Route::middleware(['auth', 'verified', 'client'])->prefix('client')->name('client.')->group(function () {
    // Dashboard
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');

    // Profile
    Route::get('/profile', [ProfileController::class, 'index'])->name('profile');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');

    // Reservations
    Route::get('/reservations', [ReservationController::class, 'index'])->name('reservations');
    Route::get('/stages', [ReservationController::class, 'stages'])->name('stages');
    Route::get('/tests', [ReservationController::class, 'tests'])->name('tests');
    Route::get('/reservation-tests', [ReservationController::class, 'reservationTests'])->name('reservation-tests');
    Route::get('/documents', [DocumentController::class, 'index'])->name('documents');
});
