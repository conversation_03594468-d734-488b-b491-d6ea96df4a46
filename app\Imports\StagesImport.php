<?php

namespace App\Imports;

use App\Models\Stage;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithValidation;
use Maatwebsite\Excel\Concerns\Importable;
use Maatwebsite\Excel\Concerns\WithUpserts;

use Maatwebsite\Excel\Concerns\SkipsOnError;
use Maatwebsite\Excel\Concerns\SkipsErrors;

class StagesImport implements ToModel, WithHeadingRow, WithValidation, SkipsOnError, WithUpserts
{
    use Importable, SkipsErrors;

    /**
     * @param array $row
     *
     * @return \Illuminate\Database\Eloquent\Model|null
     */
    public function model(array $row)
    {
        try {
            // Essayer de convertir la date
            $date_debut = null;
            $dateValue = $row['dates'];

            // Vérifier si c'est un nombre (format Excel)
            if (is_numeric($dateValue)) {
                // Convertir le nombre Excel en date
                try {
                    $date_debut = Carbon::instance(\PhpOffice\PhpSpreadsheet\Shared\Date::excelToDateTimeObject((int)$dateValue));
                } catch (\Exception $e) {
                    Log::warning("Erreur lors de la conversion de la date Excel: " . $e->getMessage());
                }
            } else {
                // Essayer différents formats de date textuels
                $dateFormats = ['d/m/Y', 'd-m-Y', 'Y-m-d'];

                foreach ($dateFormats as $format) {
                    try {
                        $date_debut = Carbon::createFromFormat($format, $dateValue);
                        break;
                    } catch (\Exception $e) {
                        continue;
                    }
                }
            }

            // Si aucun format ne fonctionne, lancer une exception
            if (!$date_debut) {
                throw new \Exception("Format de date non reconnu: {$dateValue}");
            }

            $date_fin = (clone $date_debut)->addDay(); // Ajouter un jour à la date de début

            // Générer une référence unique si non fournie
            $reference = isset($row['reference']) ? $row['reference'] : 'STAGE-' . uniqid();

            // Si un ID est fourni, l'utiliser (utile pour les tests)
            if (isset($row['id'])) {
                Log::info('ID fourni: ' . $row['id']);
                $stageData = [
                'id' => $row['id'],
                'date_debut' => $date_debut,
                'date_fin' => $date_fin,
                'lieu_id' => $row['lieu_id'],
                'places_disponibles' => $row['places_disponibles'],
                'prix' => $row['prix'],
                'reference' => $reference,
            ];
            } else {
                $stageData = [
                    'date_debut' => $date_debut,
                    'date_fin' => $date_fin,
                    'lieu_id' => $row['lieu_id'],
                    'places_disponibles' => $row['places_disponibles'],
                    'prix' => $row['prix'],
                    'reference' => $reference,
                ];
            }

            return new Stage($stageData);
        } catch (\Exception $e) {
            // Log l'erreur
            Log::error('Erreur lors de l\'importation d\'un stage: ' . $e->getMessage(), $row);
            return null;
        }
    }

    /**
     * @return array
     */
    public function rules(): array
    {
        return [
            'dates' => 'required',
            'prix' => 'required|numeric|min:0',
            'places_disponibles' => 'required|integer|min:1',
            'lieu_id' => 'required|exists:lieus,id',
            // ID est optionnel
            'id' => 'sometimes|integer',
            // Reference est optionnel
            'reference' => 'sometimes|string',
        ];
    }

    /**
     * @return array
     */
    public function customValidationMessages()
    {
        return [
            'dates.required' => 'La date est obligatoire',
            'prix.required' => 'Le prix est obligatoire',
            'prix.numeric' => 'Le prix doit être un nombre',
            'prix.min' => 'Le prix doit être supérieur ou égal à 0',
            'places_disponibles.required' => 'Le nombre de places disponibles est obligatoire',
            'places_disponibles.integer' => 'Le nombre de places disponibles doit être un nombre entier',
            'places_disponibles.min' => 'Le nombre de places disponibles doit être supérieur ou égal à 1',
            'lieu_id.required' => 'Le lieu est obligatoire',
            'lieu_id.exists' => 'Le lieu sélectionné n\'existe pas',
            'id.integer' => 'L\'ID doit être un nombre entier',
            'reference.string' => 'La référence doit être une chaîne de caractères',
        ];
    }

    /**
     * @return string|array
     */
    public function uniqueBy()
    {
        return 'id';
    }
}

