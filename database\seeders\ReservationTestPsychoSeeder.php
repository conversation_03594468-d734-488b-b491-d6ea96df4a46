<?php

namespace Database\Seeders;

use App\Models\ReservationTestPsycho;
use App\Models\TestPsycho;
use App\Models\TypeTestPsycho;
use App\Models\User;
use Illuminate\Database\Seeder;

class ReservationTestPsychoSeeder extends Seeder
{
    public function run(): void
    {
        $tests = TestPsycho::all();
        $users = User::where('role', 'client')->get();
        $types = TypeTestPsycho::all();
        
        if ($tests->isEmpty() || $users->isEmpty() || $types->isEmpty()) {
            // Si aucun test, utilisateur ou type n'existe, on ne peut pas créer de réservations
            return;
        }

        // Créer 20 réservations de tests psychotechniques
        for ($i = 0; $i < 20; $i++) {
            ReservationTestPsycho::create([
                'test_psycho_id' => $tests->random()->id,
                'user_id' => $users->random()->id,
                'type_test_psycho_id' => $types->random()->id,
                'date_reservation' => now()->subDays(rand(1, 30)),
                'statut' => ['confirmée', 'en attente', 'annulée'][rand(0, 2)],
                'motif' => 'Motif de test ' . $i,
                'permis_recto' => 'permis/recto/sample_' . $i . '.pdf',
                'permis_verso' => 'permis/verso/sample_' . $i . '.pdf',
                'document_tribunal' => 'tribunal/sample_' . $i . '.pdf',
            ]);
        }
    }
}
