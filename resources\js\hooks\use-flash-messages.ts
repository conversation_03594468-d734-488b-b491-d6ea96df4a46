import { usePage } from "@inertiajs/react";
import { useEffect } from "react";
import { toast } from "sonner";
import { SharedData } from "@/types";

export function useFlashMessages() {
  const { flash } = usePage<SharedData>().props;

  useEffect(() => {
    if (flash.success) {
      toast.success(flash.success);
    }

    if (flash.error) {
      toast.error(flash.error);
    }

    if (flash.warning) {
      toast.warning(flash.warning);
    }

    if (flash.info) {
      toast.info(flash.info);
    }
  }, [flash]);

  return null;
}
