import FrontLayout from '@/layouts/front-layout';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Link } from '@inertiajs/react';
import { format, parseISO } from 'date-fns';
import { fr } from 'date-fns/locale';
import { CheckCircle2, Calendar, MapPin, Brain, Euro, FileText } from 'lucide-react';
import { Head } from '@inertiajs/react';
import { ReservationTestPsycho } from '@/types';

interface ConfirmationTestProps {
  reservation: ReservationTestPsycho & {
    test_psycho: {
      prix: number;
      date: string;

      reference: string;
      lieu: {
        nom: string;
        ville: {
          nom: string;
          departement: {
            nom: string;
          };
        };
      };
    };
    type_test_psycho: {
      nom: string;
    };
  };
}

export default function ConfirmationTest({ reservation }: ConfirmationTestProps) {
  return (
    <FrontLayout title="Confirmation de réservation">
      <Head title="Confirmation de réservation" />
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8 flex items-center">
          <div className="flex items-center gap-2">
            <div className="flex h-8 w-8 items-center justify-center rounded-full bg-green-500 text-white">
              <span className="text-sm">✓</span>
            </div>
            <span className="font-medium">Authentification</span>
          </div>
          <div className="mx-2 h-px w-8 bg-gray-300"></div>
          <div className="flex items-center gap-2">
            <div className="flex h-8 w-8 items-center justify-center rounded-full bg-green-500 text-white">
              <span className="text-sm">✓</span>
            </div>
            <span className="font-medium">Paiement</span>
          </div>
          <div className="mx-2 h-px w-8 bg-gray-300"></div>
          <div className="flex items-center gap-2">
            <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary text-white">3</div>
            <span className="font-medium">Confirmation</span>
          </div>
        </div>

        <div className="mb-8 flex flex-col items-center justify-center text-center">
          <CheckCircle2 className="mb-4 h-16 w-16 text-green-500" />
          <h1 className="mb-2 text-3xl font-bold">Réservation confirmée</h1>
          <p className="text-lg text-muted-foreground">
            Votre réservation pour le test psychotechnique a été enregistrée avec succès.
          </p>
        </div>

        <div className="grid gap-6 md:grid-cols-2">
          <Card>
            <CardHeader>
              <CardTitle>Détails du test</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-start gap-3">
                <Brain className="mt-1 h-5 w-5 text-muted-foreground" />
                <div>
                  <h3 className="font-semibold">Type de test</h3>
                  <p>{reservation.type_test_psycho.nom}</p>
                </div>
              </div>

              <div className="flex items-start gap-3">
                <Calendar className="mt-1 h-5 w-5 text-muted-foreground" />
                <div>
                  <h3 className="font-semibold">Date</h3>
                  <p>{format(parseISO(reservation.test_psycho.date), 'dd MMMM yyyy', { locale: fr })}</p>
                </div>
              </div>



              <div className="flex items-start gap-3">
                <MapPin className="mt-1 h-5 w-5 text-muted-foreground" />
                <div>
                  <h3 className="font-semibold">Lieu</h3>
                  <p>{reservation.test_psycho.lieu.nom}</p>
                  <p className="text-sm text-muted-foreground">
                    {reservation.test_psycho.lieu.ville.nom}, {reservation.test_psycho.lieu.ville.departement.nom}
                  </p>
                </div>
              </div>

              <div className="flex items-start gap-3">
                <Euro className="mt-1 h-5 w-5 text-muted-foreground" />
                <div>
                  <h3 className="font-semibold">Prix</h3>
                  <p>{reservation.test_psycho.prix} €</p>
                </div>
              </div>

              <div className="flex items-start gap-3">
                <FileText className="mt-1 h-5 w-5 text-muted-foreground" />
                <div>
                  <h3 className="font-semibold">Référence</h3>
                  <p>{reservation.test_psycho.reference}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Informations importantes</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h3 className="mb-2 font-semibold">Documents à apporter</h3>
                <ul className="list-disc pl-5 space-y-1">
                  <li>Pièce d'identité en cours de validité</li>
                  <li>Permis de conduire (si vous en êtes encore titulaire)</li>
                  <li>Document du tribunal ou de la préfecture</li>
                  <li>Cette confirmation de réservation</li>
                </ul>
              </div>

              <div>
                <h3 className="mb-2 font-semibold">Conseils</h3>
                <ul className="list-disc pl-5 space-y-1">
                  <li>Présentez-vous 15 minutes avant l'heure du test</li>
                  <li>Assurez-vous d'être reposé(e) le jour du test</li>
                  <li>Portez vos lunettes ou lentilles si vous en avez besoin</li>
                  <li>Évitez la consommation d'alcool ou de médicaments pouvant altérer votre vigilance</li>
                </ul>
              </div>

              <div>
                <h3 className="mb-2 font-semibold">Statut de votre réservation</h3>
                <div className="rounded-md bg-green-50 p-3 text-green-800 dark:bg-green-900 dark:text-green-100">
                  <p className="font-medium">
                    {reservation.statut === 'confirmée'
                      ? 'Confirmée'
                      : reservation.statut === 'en attente'
                      ? 'En attente de confirmation'
                      : 'Annulée'}
                  </p>
                  <p className="text-sm">
                    {reservation.statut === 'confirmée'
                      ? 'Votre réservation est confirmée. Vous recevrez un email de confirmation.'
                      : reservation.statut === 'en attente'
                      ? 'Votre réservation est en attente de confirmation. Nous vous contacterons prochainement.'
                      : 'Votre réservation a été annulée.'}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="mt-8 flex justify-center gap-4">
          <Button asChild variant="outline">
            <Link href={route('client.dashboard')}>Accéder à mon espace client</Link>
          </Button>
          <Button asChild>
            <Link href={route('home')}>Retour à l'accueil</Link>
          </Button>
        </div>
      </div>
    </FrontLayout>
  );
}
