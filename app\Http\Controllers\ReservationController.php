<?php

namespace App\Http\Controllers;

use App\Models\Stage;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;

class ReservationController extends Controller
{
    public function show($stageId)
    {
        // Vérifier si l'utilisateur est connecté
        if (!Auth::check()) {
            // Stocker l'ID du stage dans la session pour redirection après connexion
            session(['redirect_after_login' => route('reservation.show', $stageId)]);

            // Rediriger vers la page de connexion
            return redirect()->route('login')->with('message', 'Veuillez vous connecter pour effectuer une réservation.');
        }

        // Récupérer le stage
        $stage = Stage::with(['lieu.ville.departement'])->findOrFail($stageId);

        // Afficher le formulaire de réservation
        return Inertia::render('Front/Reservation', [
            'stage' => $stage,
            'user' => Auth::user(),
        ]);
    }

    public function store(Request $request)
    {
        // Valider les données du formulaire
        $validated = $request->validate([
            'stage_id' => 'required|exists:stages,id',
            'cas' => 'required|in:1,2,3,4',
            'date_infr' => 'nullable|date',
            'heure_infr' => 'nullable',
            'lieu_infr' => 'nullable|string',
            // Ajouter d'autres validations selon les besoins
        ]);

        // Traiter les fichiers téléchargés si nécessaire
        if ($request->hasFile('recto')) {
            $rectoPath = $request->file('recto')->store('documents', 'public');
            $validated['recto_path'] = $rectoPath;
        }

        if ($request->hasFile('verso')) {
            $versoPath = $request->file('verso')->store('documents', 'public');
            $validated['verso_path'] = $versoPath;
        }

        if ($request->hasFile('recto48')) {
            $recto48Path = $request->file('recto48')->store('documents', 'public');
            $validated['recto48_path'] = $recto48Path;
        }

        if ($request->hasFile('verso48')) {
            $verso48Path = $request->file('verso48')->store('documents', 'public');
            $validated['verso48_path'] = $verso48Path;
        }

        // Créer la réservation
        $reservation = Auth::user()->reservations()->create([
            'stage_id' => $validated['stage_id'],
            'type_stage_id' => $validated['cas'], // Ajouter le type_stage_id
            'cas' => $validated['cas'],
            'date_infr' => $validated['date_infr'] ?? null,
            'heure_infr' => $validated['heure_infr'] ?? null,
            'lieu_infr' => $validated['lieu_infr'] ?? null,
            'recto_path' => $validated['recto_path'] ?? null,
            'verso_path' => $validated['verso_path'] ?? null,
            'recto48_path' => $validated['recto48_path'] ?? null,
            'verso48_path' => $validated['verso48_path'] ?? null,
            'statut' => 'en attente',
            'date_reservation' => now(),
        ]);

        // Rediriger vers la page de paiement
        return redirect()->route('paiement', $reservation->id);
    }
}



