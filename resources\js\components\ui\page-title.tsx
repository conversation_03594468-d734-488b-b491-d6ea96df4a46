import { cn } from "@/lib/utils";
import { motion } from "framer-motion";
import { Badge } from "@/components/ui/badge";
import { ArrowRightIcon } from "lucide-react";
import { Button } from "@/components/ui/button";

interface PageTitleAction {
  text: string;
  href: string;
  icon?: React.ReactNode;
  variant?: "default" | "outline" | "secondary";
}

interface PageTitleProps {
  badge?: {
    text: string;
    action?: {
      text: string;
      href: string;
    };
  };
  title: string;
  subtitle?: string;
  description?: string;
  actions?: PageTitleAction[];
  className?: string;
}

export function PageTitle({
  badge,
  title,
  subtitle,
  description,
  actions,
  className
}: PageTitleProps) {
  return (
    <div className={cn(
      "relative overflow-hidden py-12 bg-background text-foreground px-4",
      "fade-bottom",
      className
    )}>
      {/* Animated gradient background */}
      <motion.div
        className="absolute inset-0 -z-10 opacity-50"
        animate={{
          background: [
            "linear-gradient(45deg, rgb(var(--primary) / 0.1) 0%, rgb(var(--primary) / 0.05) 100%)",
            "linear-gradient(225deg, rgb(var(--primary) / 0.1) 0%, rgb(var(--primary) / 0.05) 100%)",
          ],
        }}
        transition={{
          duration: 5,
          repeat: Infinity,
          repeatType: "reverse",
        }}
      />

      {/* Decorative elements */}
      <div className="absolute inset-0 -z-10">
        <motion.div
          className="absolute left-1/2 top-0 h-[1px] w-1/4 bg-gradient-to-r from-transparent via-primary/20 to-transparent"
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.5 }}
        />
        <motion.div
          className="absolute bottom-0 left-1/2 h-[1px] w-1/4 bg-gradient-to-r from-transparent via-primary/20 to-transparent"
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        />
      </div>

      <div className="container relative mx-auto max-w-4xl">
        <div className="flex flex-col items-center gap-6 text-center">
          {/* Badge */}
          {badge && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <Badge variant="outline" className="gap-2">
                <span className="text-muted-foreground">{badge.text}</span>
                {badge.action && (
                  <a href={badge.action.href} className="flex items-center gap-1">
                    {badge.action.text}
                    <ArrowRightIcon className="h-3 w-3" />
                  </a>
                )}
              </Badge>
            </motion.div>
          )}

          {/* Title */}
          <motion.h1
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
            className="bg-gradient-to-r from-foreground to-muted-foreground bg-clip-text text-4xl font-bold tracking-tight text-transparent sm:text-5xl"
          >
            {title}
          </motion.h1>

          {/* Subtitle */}
          {subtitle && (
            <motion.h2
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              className="text-xl text-muted-foreground"
            >
              {subtitle}
            </motion.h2>
          )}

          {/* Description */}
          {description && (
            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.3 }}
              className="max-w-[550px] text-muted-foreground"
            >
              {description}
            </motion.p>
          )}

          {/* Actions */}
          {actions && actions.length > 0 && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.4 }}
              className="flex flex-wrap justify-center gap-4 pt-4"
            >
              {actions.map((action, index) => (
                <Button
                  key={index}
                  variant={action.variant}
                  asChild
                >
                  <a href={action.href} className="flex items-center gap-2">
                    {action.icon}
                    {action.text}
                  </a>
                </Button>
              ))}
            </motion.div>
          )}
        </div>
      </div>
    </div>
  );
}


