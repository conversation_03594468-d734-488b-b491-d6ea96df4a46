<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Inertia\Inertia;
use App\Models\Stage;
use App\Models\Departement;
use App\Models\TestPsycho;
use App\Models\TypeTestPsycho;

class FrontController extends Controller
{
    public function welcome()
    {
        // Récupérer les départements qui ont des stages en cours (dates futures)
        $departements = Departement::whereHas('villes.lieus.stages', function ($query) {
            $query->where('date_debut', '>', now());
        })->get();

        // Forcer l'ajout de quelques départements pour tester
        if ($departements->isEmpty()) {
            // Si aucun département n'a de stages, on crée une collection avec des départements de test
            $departements = collect([
                (object)['id' => 1, 'nom' => 'Ain', 'code' => '01'],
                (object)['id' => 13, 'nom' => 'Bouches-du-Rhône', 'code' => '13'],
                (object)['id' => 33, 'nom' => 'Gironde', 'code' => '33'],
                (object)['id' => 59, 'nom' => 'Nord', 'code' => '59'],
                (object)['id' => 75, 'nom' => 'Paris', 'code' => '75'],
            ]);
        }

        // Log pour déboguer
        // Log::info('Départements pour la carte: ' . $departements->count());
        // Log::info($departements->pluck('code')->toArray());

        return Inertia::render('Front/Welcome', [
            'departements' => $departements
        ]);
    }

    public function permisPoints()
    {
        return Inertia::render('Front/PermisPoints');
    }

    public function sensibilisationAccidents()
    {
        return Inertia::render('Front/SensibilisationAccidents');
    }

    public function stageFrancePermis()
    {
        return Inertia::render('Front/StageFrancePermis');
    }

    public function conditionsGenerales()
    {
        return Inertia::render('Front/ConditionsGenerales');
    }

    public function permisEnDanger()
    {
        return Inertia::render('Front/PermisEnDanger');
    }

    public function antai()
    {
        return Inertia::render('Front/Antai');
    }

    public function stages(Request $request)
    {
        $query = Stage::with('lieu.ville.departement')
            ->where('date_debut', '>', now());

        // Filtre par département
        if ($request->departement && $request->departement !== 'all') {
            $query->whereHas('lieu.ville.departement', function ($q) use ($request) {
                $q->where('code', $request->departement);
            });
        }

        // Filtre par mois
        if ($request->mois && $request->mois !== 'all') {
            $query->whereMonth('date_debut', $request->mois);
        }

        $stages = $query->orderBy('date_debut')->paginate(9);

        // Récupérer les départements qui ont des stages en cours
        $departements = Departement::whereHas('villes.lieus.stages', function ($query) {
            $query->where('date_debut', '>', now());
        })->get();

        return Inertia::render('Front/Stages', [
            'stages' => $stages, // Laravel va automatiquement inclure les métadonnées de pagination
            'departements' => $departements,
            'filters' => $request->only(['departement', 'mois'])
        ]);
    }

    public function testsPsychotechniques(Request $request)
    {
        $query = TestPsycho::with('lieu.ville.departement')
            ->where('date', '>', now());

        // Filtre par département
        if ($request->departement && $request->departement !== 'all') {
            $query->whereHas('lieu.ville.departement', function ($q) use ($request) {
                $q->where('code', $request->departement);
            });
        }

        // Filtre par mois
        if ($request->mois && $request->mois !== 'all') {
            $query->whereMonth('date', $request->mois);
        }

        $tests = $query->orderBy('date')->paginate(9);

        // Récupérer les départements qui ont des tests en cours
        $departements = Departement::whereHas('villes.lieus.testPsychos', function ($query) {
            $query->where('date', '>', now());
        })->get();

        // Récupérer les types de tests psychotechniques
        $typeTests = TypeTestPsycho::all();

        return Inertia::render('Front/TestsPsychotechniques', [
            'tests' => $tests,
            'departements' => $departements,
            'typeTests' => $typeTests,
            'filters' => $request->only(['departement', 'mois'])
        ]);
    }

    public function pssm()
    {
        return Inertia::render('Front/Pssm');
    }

    public function dossiers()
    {
        return Inertia::render('Front/Dossiers');
    }

    public function contact()
    {
        return Inertia::render('Front/Contact');
    }
}



