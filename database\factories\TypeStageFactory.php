<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

class TypeStageFactory extends Factory
{
    public function definition(): array
    {
        return [
            'nom' => fake()->randomElement([
                'Récupération volontaire de 4 points',
                'Stage en période probatoire',
                'Stage obligatoire',
                'Stage volontaire'
            ]),
            'description' => fake()->paragraph(),
        ];
    }
}