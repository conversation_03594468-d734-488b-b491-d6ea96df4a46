import * as React from "react"
import { type LucideIcon } from "lucide-react"
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>ip<PERSON>ontent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { cn } from "@/lib/utils"

export interface ChartConfig {
  [key: string]: {
    label: string
    color?: string
    icon?: LucideIcon
    theme?: {
      light: string
      dark: string
    }
  }
}

interface ChartContainerProps extends React.HTMLAttributes<HTMLDivElement> {
  config?: ChartConfig
}

export function ChartContainer({
  config,
  className,
  children,
  ...props
}: ChartContainerProps) {
  const [mounted, setMounted] = React.useState(false)

  React.useEffect(() => {
    setMounted(true)
    return () => setMounted(false)
  }, [])

  // Set chart colors as CSS variables
  React.useEffect(() => {
    if (!config || !mounted) return

    const root = document.documentElement
    const isDark = document.documentElement.classList.contains("dark")

    for (const [key, value] of Object.entries(config)) {
      if (value.theme) {
        const color = isDark ? value.theme.dark : value.theme.light
        root.style.setProperty(`--color-${key}`, color)
      } else if (value.color) {
        root.style.setProperty(`--color-${key}`, value.color)
      }
    }
  }, [config, mounted])

  return (
    <div
      className={cn("w-full [&_.recharts-tooltip-cursor]:fill-muted/20", className)}
      {...props}
    >
      {children}
    </div>
  )
}

interface ChartTooltipContentProps extends React.HTMLAttributes<HTMLDivElement> {
  nameKey?: string
  labelKey?: string
  valueKey?: string
  labelFormatter?: (label: string) => string
  valueFormatter?: (value: number) => string
  indicator?: "line" | "dashed"
}

export function ChartTooltipContent({
  className,
  nameKey = "name",
  labelKey = "label",
  valueKey = "value",
  labelFormatter = (label) => label,
  valueFormatter = (value) => value.toString(),
  indicator = "line",
  ...props
}: ChartTooltipContentProps) {
  const { active, payload, label } = props as any

  if (!active || !payload?.length) {
    return null
  }

  return (
    <div className="rounded-lg border bg-background p-2 shadow-md">
      <div className="grid gap-2">
        <div className="flex items-center justify-between gap-2">
          <div className="text-sm text-muted-foreground">
            {labelFormatter(label)}
          </div>
        </div>
        <div className="grid gap-1">
          {payload.map((item: any, index: number) => {
            const color = item.color || item.fill || item.stroke
            const name = item[nameKey] || item.dataKey || item.name || item.key
            const value = item[valueKey] || item.value

            return (
              <div
                key={`item-${index}`}
                className="flex items-center justify-between gap-2"
              >
                <div className="flex items-center gap-1">
                  {indicator === "line" ? (
                    <div
                      className="h-1 w-3 rounded-full"
                      style={{ backgroundColor: color }}
                    />
                  ) : (
                    <div
                      className="h-1.5 w-1.5 rounded-full"
                      style={{ backgroundColor: color }}
                    />
                  )}
                  <span className="text-xs font-medium">{name}</span>
                </div>
                <div className="text-xs font-medium tabular-nums">
                  {valueFormatter(value)}
                </div>
              </div>
            )
          })}
        </div>
      </div>
    </div>
  )
}

interface ChartLegendContentProps extends React.HTMLAttributes<HTMLDivElement> {
  nameKey?: string
  valueKey?: string
  valueFormatter?: (value: number) => string
  iconType?: "circle" | "line"
  iconSize?: number
  direction?: "row" | "column"
}

export function ChartLegendContent({
  className,
  nameKey = "name",
  valueKey = "value",
  valueFormatter = (value) => value.toString(),
  iconType = "circle",
  iconSize = 8,
  direction = "row",
  ...props
}: ChartLegendContentProps) {
  const { payload } = props as any

  if (!payload?.length) {
    return null
  }

  return (
    <div
      className={cn(
        "flex flex-wrap items-center gap-4",
        direction === "column" && "flex-col items-start",
        className
      )}
    >
      {payload.map((entry: any, index: number) => {
        const color = entry.color || entry.fill || entry.stroke
        const name = entry[nameKey] || entry.dataKey || entry.name || entry.key
        const value = entry[valueKey] || entry.value

        return (
          <div key={`item-${index}`} className="flex items-center gap-1">
            {iconType === "circle" ? (
              <div
                className="rounded-full"
                style={{
                  backgroundColor: color,
                  width: iconSize,
                  height: iconSize,
                }}
              />
            ) : (
              <div
                className="rounded-full"
                style={{
                  backgroundColor: color,
                  width: iconSize * 2,
                  height: iconSize / 2,
                }}
              />
            )}
            <span className="text-xs font-medium">{name}</span>
            {value !== undefined && (
              <span className="text-xs font-medium tabular-nums">
                {valueFormatter(value)}
              </span>
            )}
          </div>
        )
      })}
    </div>
  )
}

interface ChartTooltipProps {
  content?: React.ReactNode
  cursor?: boolean | object
  offset?: number
  position?: {
    x?: number
    y?: number
  }
  children?: React.ReactNode
}

export function ChartTooltip({
  content,
  cursor = true,
  offset = 10,
  position,
  children,
  ...props
}: ChartTooltipProps) {
  // Pour Recharts, nous devons simplement passer les props
  return (
    <div className="recharts-tooltip-wrapper" {...props}>
      {content}
    </div>
  )
}

interface ChartLegendProps {
  content?: React.ReactNode
  verticalAlign?: "top" | "middle" | "bottom"
  align?: "left" | "center" | "right"
  height?: number
  width?: number
  layout?: "horizontal" | "vertical"
  iconType?: "circle" | "line" | "rect" | "diamond"
  iconSize?: number
  children?: React.ReactNode
}

export function ChartLegend({
  content,
  verticalAlign = "bottom",
  align = "center",
  height = 36,
  width,
  layout = "horizontal",
  iconType = "circle",
  iconSize = 8,
  children,
  ...props
}: ChartLegendProps) {
  return (
    <div className="recharts-legend-wrapper" {...props}>
      {content}
    </div>
  )
}
