
import {
    SidebarGroup,
    SidebarGroupLabel,
    SidebarMenu,
    SidebarMenuButton,
    SidebarMenuItem,
    SidebarMenuSub,
    SidebarMenuSubButton,
    SidebarMenuSubItem,
    useSidebar
} from '@/components/ui/sidebar';
import { type NavItem } from '@/types';
import { Link, usePage } from '@inertiajs/react';
import { ChevronRight } from 'lucide-react';
import { useState, useEffect } from 'react';
import { cn } from '@/lib/utils';
import {
    Tooltip,
    TooltipContent,
    TooltipProvider,
    TooltipTrigger,
} from '@/components/ui/tooltip';

export function NavMainWithDropdown({ items = [] }: { items: NavItem[] }) {
    const page = usePage();
    const { state } = useSidebar();
    const [openDropdowns, setOpenDropdowns] = useState<Record<string, boolean>>({});
    const isCollapsed = state === 'collapsed';

    const toggleDropdown = (title: string) => {
        setOpenDropdowns(prev => ({
            ...prev,
            [title]: !prev[title]
        }));
    };

    const isActive = (href?: string) => {
        if (!href) return false;
        return page.url === href || page.url.startsWith(href);
    };

    const isChildActive = (children?: NavItem[]) => {
        if (!children) return false;
        return children.some(child => isActive(child.href));
    };

    // Initialize open dropdowns based on active child routes
    useEffect(() => {
        const newOpenDropdowns: Record<string, boolean> = {};

        items.forEach(item => {
            if (item.children && isChildActive(item.children)) {
                newOpenDropdowns[item.title] = true;
            }
        });

        setOpenDropdowns(prev => ({
            ...prev,
            ...newOpenDropdowns
        }));
    }, [page.url, items]);

    return (
        <SidebarGroup className="px-2 py-0">
            <SidebarMenu>
                {items.map((item) => (
                    <SidebarMenuItem key={item.title}>
                        {item.type == 'group' ?
                        <>{ !isCollapsed ? <SidebarGroupLabel>{item.title} </SidebarGroupLabel> : <>----</> } </>

                            :
                            item.children ? (
                                <>
                                    {isCollapsed ? (
                                        <TooltipProvider>
                                            <Tooltip>
                                                <TooltipTrigger asChild>
                                                    <SidebarMenuButton
                                                        onClick={() => toggleDropdown(item.title)}
                                                        isActive={isChildActive(item.children)}
                                                    >
                                                        {item.icon && <item.icon className="h-4 w-4" />}
                                                    </SidebarMenuButton>
                                                </TooltipTrigger>
                                                <TooltipContent side="right" align="center">
                                                    <div className="flex flex-col gap-1 p-1">
                                                        <div className="font-medium">{item.title}</div>
                                                        {item.children.map((child) => (
                                                            <Link
                                                                key={child.title}
                                                                href={child.href || ''}
                                                                prefetch
                                                                className={cn(
                                                                    "flex items-center gap-2 rounded px-2 py-1 text-sm hover:bg-accent hover:text-black",
                                                                    isActive(child.href) && "bg-accent text-black"
                                                                )}
                                                            >
                                                                {child.icon && <child.icon className="h-4 w-4" />}
                                                                <span>{child.title}</span>
                                                            </Link>
                                                        ))}
                                                    </div>
                                                </TooltipContent>
                                            </Tooltip>
                                        </TooltipProvider>
                                    ) : (
                                        <>
                                            <SidebarMenuButton
                                                onClick={() => toggleDropdown(item.title)}
                                                isActive={isChildActive(item.children)}
                                                tooltip={{ children: item.title }}
                                                className="flex justify-between"
                                            >
                                                <div className="flex items-center">
                                                    {item.icon && <item.icon className="mr-2 h-4 w-4" />}
                                                    <span>{item.title}</span>
                                                </div>
                                                <div className={cn(
                                                    "transition-transform duration-200",
                                                    openDropdowns[item.title] ? "rotate-90" : ""
                                                )}>
                                                    <ChevronRight className="h-4 w-4" />
                                                </div>
                                            </SidebarMenuButton>

                                            {openDropdowns[item.title] && (
                                                <SidebarMenuSub>
                                                    {item.children.map((child) => (
                                                        <SidebarMenuSubItem key={child.title}>
                                                            <SidebarMenuSubButton
                                                                asChild
                                                                isActive={isActive(child.href)}
                                                            >
                                                                <Link href={child.href || ''} prefetch>
                                                                    {child.icon && <child.icon />}
                                                                    <span>{child.title}</span>
                                                                </Link>
                                                            </SidebarMenuSubButton>
                                                        </SidebarMenuSubItem>
                                                    ))}
                                                </SidebarMenuSub>
                                            )}
                                        </>
                                    )}
                                </>
                            ) : (
                                <SidebarMenuButton
                                    asChild
                                    isActive={isActive(item.href)}
                                    tooltip={{ children: item.title }}
                                >
                                    <Link href={item.href || ''} prefetch>
                                        {item.icon && <item.icon />}
                                        {!isCollapsed && <span>{item.title}</span>}
                                    </Link>
                                </SidebarMenuButton>
                            )}
                    </SidebarMenuItem>
                ))}
            </SidebarMenu>
        </SidebarGroup>
    );
}



