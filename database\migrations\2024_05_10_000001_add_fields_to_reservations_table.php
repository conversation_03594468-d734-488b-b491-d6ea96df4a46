<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('reservations', function (Blueprint $table) {
            $table->string('cas')->nullable()->after('type_stage_id');
            $table->date('date_infr')->nullable()->after('cas');
            $table->string('heure_infr')->nullable()->after('date_infr');
            $table->string('lieu_infr')->nullable()->after('heure_infr');
            $table->string('recto_path')->nullable()->after('lieu_infr');
            $table->string('verso_path')->nullable()->after('recto_path');
            $table->string('recto48_path')->nullable()->after('verso_path');
            $table->string('verso48_path')->nullable()->after('recto48_path');
            $table->dateTime('date_paiement')->nullable()->after('date_reservation');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('reservations', function (Blueprint $table) {
            $table->dropColumn([
                'cas',
                'date_infr',
                'heure_infr',
                'lieu_infr',
                'recto_path',
                'verso_path',
                'recto48_path',
                'verso48_path',
                'date_paiement'
            ]);
        });
    }
};
