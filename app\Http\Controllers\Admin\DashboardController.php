<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Departement;
use App\Models\Lieu;
use App\Models\Reservation;
use App\Models\ReservationTestPsycho;
use App\Models\Stage;
use App\Models\TestPsycho;
use App\Models\User;
use Illuminate\Support\Facades\Log;
use Inertia\Inertia;

class DashboardController extends Controller
{
    public function index()
    {
        // Récupérer les statistiques
        $stats = [
            'totalStages' => Stage::count(),
            'stagesAVenir' => Stage::where('date_debut', '>=', now())->count(),
            'totalReservations' => Reservation::count(),
            'reservationsConfirmees' => Reservation::where('statut', 'confirmée')->count(),
            'totalTests' => TestPsycho::count(),
            'testsAVenir' => TestPsycho::where('date', '>=', now())->count(),
            'totalLieux' => Lieu::count(),
            'totalClients' => User::where('role', 'client')->count(),
            'totalDepartements' => Departement::count(),
        ];

        // Récupérer les activités récentes (5 dernières réservations et tests)
        $recentReservations = Reservation::with(['stage.lieu.ville', 'user'])
            ->orderBy('date_reservation', 'desc')
            ->take(3)
            ->get()
            ->map(function ($reservation) {
                return [
                    'id' => $reservation->id,
                    'type' => 'reservation',
                    'date' => $reservation->date_reservation,
                    'statut' => $reservation->statut,
                    'client' => [
                        'id' => $reservation->user->id,
                        'nom' => $reservation->user->nom,
                        'prenom' => $reservation->user->prenom,
                    ],
                    'details' => [
                        'id' => $reservation->stage->id,
                        'reference' => $reservation->stage->reference,
                        'date_debut' => $reservation->stage->date_debut,
                        'lieu' => [
                            'id' => $reservation->stage->lieu->id,
                            'nom' => $reservation->stage->lieu->nom,
                            'ville' => [
                                'nom' => $reservation->stage->lieu->ville->nom,
                            ],
                        ],
                    ],
                ];
            });

        $recentTests = ReservationTestPsycho::with(['testPsycho.lieu.ville', 'user'])
            ->orderBy('date_reservation', 'desc')
            ->take(2)
            ->get()
            ->map(function ($reservation) {
                return [
                    'id' => $reservation->id,
                    'type' => 'test',
                    'date' => $reservation->date_reservation,
                    'statut' => $reservation->statut,
                    'client' => [
                        'id' => $reservation->user->id,
                        'nom' => $reservation->user->nom,
                        'prenom' => $reservation->user->prenom,
                    ],
                    'details' => [
                        'id' => $reservation->testPsycho->id,
                        'date' => $reservation->testPsycho->date,
                        'lieu' => [
                            'id' => $reservation->testPsycho->lieu->id,
                            'nom' => $reservation->testPsycho->lieu->nom,
                            'ville' => [
                                'nom' => $reservation->testPsycho->lieu->ville->nom,
                            ],
                        ],
                    ],
                ];
            });

        // Combiner et trier les activités récentes
        $recentActivities = $recentReservations->concat($recentTests)
            ->sortByDesc('date')
            ->values()
            ->take(5);

        // Récupérer les données pour les graphiques des réservations
        $chartData = $this->getReservationsChartData();

        // Récupérer les données pour les graphiques des réservations par statut
        $stageChartData = $this->getStageReservationsChartData();
        $testChartData = $this->getTestReservationsChartData();

        return Inertia::render('dashboard', [
            'dashboardData' => [
                'stats' => $stats,
                'recentActivities' => $recentActivities,
                'chartData' => $chartData['data'],
                'stageTrend' => $chartData['stageTrend'],
                'testTrend' => $chartData['testTrend'],
                'globalTrend' => $chartData['globalTrend'],
                'stageChartData' => $stageChartData,
                'testChartData' => $testChartData,
            ],
        ]);
    }

    /**
     * Récupère les données pour les graphiques des réservations
     *
     * @return array
     */
    private function getReservationsChartData()
    {
        $months = [];
        $chartData = [];

        // Récupérer les 6 derniers mois
        $frenchMonths = [
            'January' => 'Janvier',
            'February' => 'Février',
            'March' => 'Mars',
            'April' => 'Avril',
            'May' => 'Mai',
            'June' => 'Juin',
            'July' => 'Juillet',
            'August' => 'Août',
            'September' => 'Septembre',
            'October' => 'Octobre',
            'November' => 'Novembre',
            'December' => 'Décembre'
        ];

        for ($i = 5; $i >= 0; $i--) {
            $date = now()->subMonths($i);
            $monthKey = $date->format('Y-m');
            $englishMonth = $date->format('F');
            $monthName = $frenchMonths[$englishMonth] ?? $englishMonth;

            $months[$monthKey] = [
                'month' => $monthName,
                'stages' => 0,
                'tests' => 0
            ];
        }

        // Récupérer les réservations de stages par mois
        $stageReservations = Reservation::selectRaw('COUNT(*) as count, DATE_FORMAT(date_reservation, "%Y-%m") as month')
            ->where('date_reservation', '>=', now()->subMonths(6))
            ->groupBy('month')
            ->get();

        foreach ($stageReservations as $reservation) {
            if (isset($months[$reservation->month])) {
                $months[$reservation->month]['stages'] = $reservation->count;
            }
        }

        // Récupérer les réservations de tests psychotechniques par mois
        $testReservations = ReservationTestPsycho::selectRaw('COUNT(*) as count, DATE_FORMAT(date_reservation, "%Y-%m") as month')
            ->where('date_reservation', '>=', now()->subMonths(6))
            ->groupBy('month')
            ->get();

        foreach ($testReservations as $reservation) {
            if (isset($months[$reservation->month])) {
                $months[$reservation->month]['tests'] = $reservation->count;
            }
        }

        // Convertir en tableau pour le graphique
        foreach ($months as $data) {
            $chartData[] = $data;
        }

        // Calculer la tendance pour les stages
        $stageTrend = $this->calculateTrend($chartData, 'stages');

        // Calculer la tendance pour les tests psychotechniques
        $testTrend = $this->calculateTrend($chartData, 'tests');

        // Calculer la tendance globale
        $currentMonth = $chartData[count($chartData) - 1];
        $previousMonth = $chartData[count($chartData) - 2];

        $currentTotal = $currentMonth['stages'] + $currentMonth['tests'];
        $previousTotal = $previousMonth['stages'] + $previousMonth['tests'];

        $percentageChange = 0;
        $isUp = true;

        if ($previousTotal > 0) {
            $percentageChange = round((($currentTotal - $previousTotal) / $previousTotal) * 100);
            $isUp = $percentageChange >= 0;
            $percentageChange = abs($percentageChange);
        }

        $result = [
            'data' => $chartData,
            'stageTrend' => $stageTrend,
            'testTrend' => $testTrend,
            'globalTrend' => [
                'percentage' => $percentageChange,
                'isUp' => $isUp
            ]
        ];

        // Débogage des données
        Log::info('Chart Data', ['data' => $result]);

        return $result;
    }

    /**
     * Calcule la tendance pour un type de données spécifique
     *
     * @param array $data Les données du graphique
     * @param string $key La clé pour laquelle calculer la tendance
     * @return array
     */
    private function calculateTrend($data, $key)
    {
        $currentMonth = $data[count($data) - 1];
        $previousMonth = $data[count($data) - 2];

        $current = $currentMonth[$key];
        $previous = $previousMonth[$key];

        $percentageChange = 0;
        $isUp = true;

        if ($previous > 0) {
            $percentageChange = round((($current - $previous) / $previous) * 100);
            $isUp = $percentageChange >= 0;
            $percentageChange = abs($percentageChange);
        }

        return [
            'percentage' => $percentageChange,
            'isUp' => $isUp
        ];
    }

    /**
     * Récupère les données pour le graphique des réservations de stages par statut
     *
     * @return array
     */
    private function getStageReservationsChartData()
    {
        $chartData = [];

        // Récupérer les 90 derniers jours
        for ($i = 89; $i >= 0; $i--) {
            $date = now()->subDays($i);
            $dateKey = $date->format('Y-m-d');

            $chartData[$dateKey] = [
                'date' => $dateKey,
                'confirmée' => 0,
                'en attente' => 0,
                'annulée' => 0
            ];
        }

        // Récupérer les réservations de stages par jour et par statut
        $stageReservations = Reservation::selectRaw('COUNT(*) as count, DATE(date_reservation) as date, statut')
            ->where('date_reservation', '>=', now()->subDays(90))
            ->groupBy('date', 'statut')
            ->get();

        foreach ($stageReservations as $reservation) {
            if (isset($chartData[$reservation->date])) {
                $chartData[$reservation->date][$reservation->statut] = $reservation->count;
            }
        }

        // Convertir en tableau pour le graphique
        return array_values($chartData);
    }

    /**
     * Récupère les données pour le graphique des réservations de tests psychotechniques par statut
     *
     * @return array
     */
    private function getTestReservationsChartData()
    {
        $chartData = [];

        // Récupérer les 90 derniers jours
        for ($i = 89; $i >= 0; $i--) {
            $date = now()->subDays($i);
            $dateKey = $date->format('Y-m-d');

            $chartData[$dateKey] = [
                'date' => $dateKey,
                'confirmée' => 0,
                'en attente' => 0,
                'annulée' => 0
            ];
        }

        // Récupérer les réservations de tests psychotechniques par jour et par statut
        $testReservations = ReservationTestPsycho::selectRaw('COUNT(*) as count, DATE(date_reservation) as date, statut')
            ->where('date_reservation', '>=', now()->subDays(90))
            ->groupBy('date', 'statut')
            ->get();

        foreach ($testReservations as $reservation) {
            if (isset($chartData[$reservation->date])) {
                $chartData[$reservation->date][$reservation->statut] = $reservation->count;
            }
        }

        // Convertir en tableau pour le graphique
        return array_values($chartData);
    }
}
