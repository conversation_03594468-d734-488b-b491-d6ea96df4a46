@extends('emails.layout')

@section('title', 'Confirmation de réservation - Stage de récupération de points')

@section('header-title', 'Nouvelle Réservation')
@section('header-subtitle', 'Stage de récupération de points confirmé')

@section('content')
    <div class="greeting">
        Bonjour,
    </div>
    
    <p style="font-size: 16px; margin-bottom: 25px; color: #495057;">
        Une nouvelle réservation de stage de récupération de points vient d'être ajoutée avec succès.
    </p>
    
    <div class="highlight-box">
        <div class="amount">Réservation #{{ $reservation->id }}</div>
        <div>{{ $typeStage->nom ?? 'Stage de récupération de points' }}</div>
    </div>
    
    <div class="content-section">
        <h2 class="section-title">📋 Détails de la réservation</h2>
        <div class="info-grid">
            <div class="info-row">
                <span class="info-label">Numéro de réservation :</span>
                <span class="info-value"><strong>#{{ $reservation->id }}</strong></span>
            </div>
            <div class="info-row">
                <span class="info-label">Type de stage :</span>
                <span class="info-value">{{ $typeStage->nom ?? 'Non spécifié' }}</span>
            </div>
            <div class="info-row">
                <span class="info-label">Date de réservation :</span>
                <span class="info-value">{{ $reservation->date_reservation->format('d/m/Y à H:i') }}</span>
            </div>
            <div class="info-row">
                <span class="info-label">Statut :</span>
                <span class="info-value">
                    <span class="status-badge status-{{ strtolower($reservation->statut) }}">
                        {{ ucfirst($reservation->statut) }}
                    </span>
                </span>
            </div>
            <div class="info-row">
                <span class="info-label">Méthode de paiement :</span>
                <span class="info-value">{{ ucfirst($reservation->methode_paiement) }}</span>
            </div>
            @if($reservation->date_paiement)
            <div class="info-row">
                <span class="info-label">Date de paiement :</span>
                <span class="info-value">{{ $reservation->date_paiement->format('d/m/Y à H:i') }}</span>
            </div>
            @endif
            @if($reservation->transaction_id)
            <div class="info-row">
                <span class="info-label">ID de transaction :</span>
                <span class="info-value">{{ $reservation->transaction_id }}</span>
            </div>
            @endif
        </div>
    </div>
    
    <div class="content-section">
        <h2 class="section-title">👤 Informations du client</h2>
        <div class="info-grid">
            <div class="info-row">
                <span class="info-label">Nom complet :</span>
                <span class="info-value">{{ $user->nom }} {{ $user->prenom }}</span>
            </div>
            <div class="info-row">
                <span class="info-label">Email :</span>
                <span class="info-value">{{ $user->email }}</span>
            </div>
            @if($user->telephone)
            <div class="info-row">
                <span class="info-label">Téléphone :</span>
                <span class="info-value">{{ $user->telephone }}</span>
            </div>
            @endif
        </div>
    </div>
    
    <div class="content-section">
        <h2 class="section-title">📅 Détails du stage</h2>
        <div class="info-grid">
            <div class="info-row">
                <span class="info-label">Date du stage :</span>
                <span class="info-value"><strong>{{ $stage->date_debut->format('d/m/Y') }}</strong></span>
            </div>
            <div class="info-row">
                <span class="info-label">Prix :</span>
                <span class="info-value"><strong>{{ number_format($stage->prix, 2, ',', ' ') }} €</strong></span>
            </div>
            @if($stage->lieu)
                <div class="info-row">
                    <span class="info-label">Lieu :</span>
                    <span class="info-value">{{ $stage->lieu->nom }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">Adresse :</span>
                    <span class="info-value">{{ $stage->lieu->adresse }}</span>
                </div>
                @if($stage->lieu->ville)
                <div class="info-row">
                    <span class="info-label">Ville :</span>
                    <span class="info-value">{{ $stage->lieu->ville->nom }} ({{ $stage->lieu->ville->code_postal }})</span>
                </div>
                @endif
            @endif
        </div>
    </div>
    
    <div class="divider"></div>
    
    <div style="text-align: center;">
        <a href="{{ url('/admin/reservations/' . $reservation->id) }}" class="btn">
            Voir la réservation complète
        </a>
    </div>
    
    <p style="font-size: 14px; color: #6c757d; text-align: center; margin-top: 30px;">
        Cette notification a été générée automatiquement par le système de réservation.
    </p>
@endsection
