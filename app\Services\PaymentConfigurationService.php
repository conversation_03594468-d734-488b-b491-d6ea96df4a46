<?php

namespace App\Services;

use App\Models\Configuration;

class PaymentConfigurationService
{
    /**
     * Get all enabled payment methods
     */
    public function getEnabledPaymentMethods(): array
    {
        $paymentMethods = [
            'paypal' => Configuration::get('payment_method_paypal_enabled', true),
            'sumup' => Configuration::get('payment_method_sumup_enabled', true),
            'virement' => Configuration::get('payment_method_virement_enabled', true),
            'cheque' => Configuration::get('payment_method_cheque_enabled', true),
            'bon' => Configuration::get('payment_method_bon_enabled', true),
            'paiement_sur_place' => Configuration::get('payment_method_paiement_sur_place_enabled', true),
        ];

        // Filter only enabled methods
        return array_keys(array_filter($paymentMethods));
    }

    /**
     * Check if a payment method is enabled
     */
    public function isPaymentMethodEnabled(string $method): bool
    {
        return Configuration::get("payment_method_{$method}_enabled", true);
    }

    /**
     * Get validation rules for enabled payment methods
     */
    public function getPaymentMethodValidationRules(): string
    {
        $enabledMethods = $this->getEnabledPaymentMethods();
        
        if (empty($enabledMethods)) {
            // If no methods are enabled, allow all for safety
            return 'required|string|in:paypal,sumup,virement,cheque,bon,paiement_sur_place';
        }
        
        return 'required|string|in:' . implode(',', $enabledMethods);
    }

    /**
     * Get payment method configurations for frontend
     */
    public function getPaymentMethodsForFrontend(): array
    {
        return [
            'paypal' => [
                'enabled' => Configuration::get('payment_method_paypal_enabled', true),
                'name' => 'PayPal',
                'description' => 'Paiement sécurisé via PayPal'
            ],
            'sumup' => [
                'enabled' => Configuration::get('payment_method_sumup_enabled', true),
                'name' => 'SumUp',
                'description' => 'Paiement par carte bancaire via SumUp'
            ],
            'virement' => [
                'enabled' => Configuration::get('payment_method_virement_enabled', true),
                'name' => 'Virement bancaire',
                'description' => 'Paiement par virement bancaire'
            ],
            'cheque' => [
                'enabled' => Configuration::get('payment_method_cheque_enabled', true),
                'name' => 'Chèque',
                'description' => 'Paiement par chèque'
            ],
            'bon' => [
                'enabled' => Configuration::get('payment_method_bon_enabled', true),
                'name' => 'Bon de commande',
                'description' => 'Paiement par bon de commande'
            ],
            'paiement_sur_place' => [
                'enabled' => Configuration::get('payment_method_paiement_sur_place_enabled', true),
                'name' => 'Paiement sur place',
                'description' => 'Paiement directement sur le lieu'
            ],
        ];
    }
}
