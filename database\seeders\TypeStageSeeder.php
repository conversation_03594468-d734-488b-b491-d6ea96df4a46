<?php

namespace Database\Seeders;

use App\Models\TypeStage;
use Illuminate\Database\Seeder;

class TypeStageSeeder extends Seeder
{
    public function run(): void
    {
        $types = [
            [
                'nom' => 'Récupération volontaire de 4 points',
                'description' => 'Stage volontaire pour récupérer jusqu\'à 4 points sur votre permis de conduire.',
            ],
            [
                'nom' => 'Stage en période probatoire (joindre lettre 48N)',
                'description' => 'Stage spécial pour les conducteurs en période probatoire.',
            ],
            [
                'nom' => 'Alternative aux poursuites ou composition pénale',
                'description' => 'Alternative aux poursuites ou composition pénale.',
            ],
            [
                'nom' => 'Peine complémentaire ou mise à l\'épreuve',
                'description' => 'Peine complémentaire ou mise à l\'épreuve.',
            ],
        ];

        foreach ($types as $type) {
            TypeStage::factory()->create($type);
        }
    }
}
