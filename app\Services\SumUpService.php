<?php

namespace App\Services;

use Illuminate\Support\Facades\Log;
use SumUp\SumUp;
use SumUp\Exceptions\SumUpSDKException;
use SumUp\Exceptions\SumUpAuthenticationException;
use SumUp\Exceptions\SumUpResponseException;

class SumUpService
{
    protected $sumup;
    protected $config;

    public function __construct()
    {
        $this->config = config('sumup');
        $mode = $this->config['mode'];
        
        try {
            // Initialize SumUp SDK with the appropriate credentials based on mode
            $this->sumup = new SumUp([
                'app_id'     => $this->config[$mode]['app_id'],
                'app_secret' => $this->config[$mode]['app_secret'],
                'scopes'     => $this->config[$mode]['scopes'],
            ]);
            
            // If we have an access token, use it
            if (!empty($this->config['access_token'])) {
                $this->sumup = new SumUp([
                    'app_id'       => $this->config[$mode]['app_id'],
                    'app_secret'   => $this->config[$mode]['app_secret'],
                    'scopes'       => $this->config[$mode]['scopes'],
                    'access_token' => $this->config['access_token']
                ]);
            }
            // If we have a refresh token, use it to get a new access token
            elseif (!empty($this->config['refresh_token'])) {
                $this->sumup = new SumUp([
                    'app_id'        => $this->config[$mode]['app_id'],
                    'app_secret'    => $this->config[$mode]['app_secret'],
                    'scopes'        => $this->config[$mode]['scopes'],
                    'refresh_token' => $this->config['refresh_token']
                ]);
                
                // Refresh the token
                $this->sumup->refreshToken();
            }
            // Otherwise, use client credentials flow
            else {
                $this->sumup = new SumUp([
                    'app_id'     => $this->config[$mode]['app_id'],
                    'app_secret' => $this->config[$mode]['app_secret'],
                    'grant_type' => 'client_credentials',
                    'scopes'     => $this->config[$mode]['scopes']
                ]);
            }
        } catch (SumUpAuthenticationException $e) {
            Log::error('SumUp Authentication Error: ' . $e->getMessage());
            throw $e;
        } catch (SumUpSDKException $e) {
            Log::error('SumUp SDK Error: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Create a checkout session
     *
     * @param float $amount
     * @param string $currency
     * @param string $checkoutRef
     * @param string $description
     * @param string $returnUrl
     * @return array
     */
    public function createCheckout(float $amount, string $currency, string $checkoutRef, string $description = '', string $returnUrl = null)
    {
        try {
            $checkoutService = $this->sumup->getCheckoutService();
            
            // If no return URL is provided, use the one from config
            if (empty($returnUrl) && !empty($this->config['return_url'])) {
                $returnUrl = $this->config['return_url'];
            }
            
            // Create the checkout
            $response = $checkoutService->create(
                $amount,
                $currency,
                $checkoutRef,
                config('mail.from.address'), // Pay to email (merchant email)
                $description,
                null, // Pay from email (customer email, optional)
                $returnUrl
            );
            
            return [
                'success' => true,
                'checkout_id' => $response->getBody()->id,
                'checkout_url' => $response->getBody()->checkout_reference,
            ];
        } catch (SumUpAuthenticationException $e) {
            Log::error('SumUp Authentication Error: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => 'Authentication error: ' . $e->getMessage(),
            ];
        } catch (SumUpResponseException $e) {
            Log::error('SumUp Response Error: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => 'Response error: ' . $e->getMessage(),
            ];
        } catch (SumUpSDKException $e) {
            Log::error('SumUp SDK Error: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => 'SumUp SDK error: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Find a checkout by ID
     *
     * @param string $checkoutId
     * @return array
     */
    public function findCheckoutById(string $checkoutId)
    {
        try {
            $checkoutService = $this->sumup->getCheckoutService();
            $response = $checkoutService->findById($checkoutId);
            
            return [
                'success' => true,
                'checkout' => $response->getBody(),
            ];
        } catch (SumUpSDKException $e) {
            Log::error('SumUp SDK Error: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => 'SumUp SDK error: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Find a checkout by reference ID
     *
     * @param string $referenceId
     * @return array
     */
    public function findCheckoutByReferenceId(string $referenceId)
    {
        try {
            $checkoutService = $this->sumup->getCheckoutService();
            $response = $checkoutService->findByReferenceId($referenceId);
            
            return [
                'success' => true,
                'checkout' => $response->getBody(),
            ];
        } catch (SumUpSDKException $e) {
            Log::error('SumUp SDK Error: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => 'SumUp SDK error: ' . $e->getMessage(),
            ];
        }
    }
}
