import { useAppearance } from '@/hooks/use-appearance';
import { Button } from '@/components/ui/button';
import { Monitor, Moon, Sun } from 'lucide-react';
import { HTMLAttributes } from 'react';

export default function AppearanceToggle({ className = '', ...props }: HTMLAttributes<HTMLDivElement>) {
    const { appearance, updateAppearance } = useAppearance();

    const handleClick = () => {
        switch (appearance) {
            case 'light':
                updateAppearance('dark');
                break;
            case 'dark':
                updateAppearance('system');
                break;
            case 'system':
                updateAppearance('light');
                break;
        }
    };

    const getCurrentIcon = () => {
        switch (appearance) {
            case 'light':
                return <Sun className="h-5 w-5" />;
            case 'dark':
                return <Moon className="h-5 w-5" />;
            default:
                return <Monitor className="h-5 w-5" />;
        }
    };

    const getTitle = () => {
        switch (appearance) {
            case 'light':
                return 'Light mode';
            case 'dark':
                return 'Dark mode';
            default:
                return 'System mode';
        }
    };

    return (
        <div className={className} {...props}>
            <Button
                variant="ghost"
                size="icon"
                className="h-9 w-9 rounded-md relative group z-50"
                onClick={handleClick}
                title={getTitle()}
            >
                {getCurrentIcon()}
                <span className="sr-only">Toggle theme</span>
                <span className="absolute -bottom-8 scale-0 transition-all rounded bg-gray-900 dark:bg-gray-100 p-2 text-xs text-white dark:text-gray-900 group-hover:scale-100">
                    {getTitle()}
                </span>
            </Button>
        </div>
    );
}
