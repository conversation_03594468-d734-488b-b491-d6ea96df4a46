import { Head, router } from '@inertiajs/react';
import { BreadcrumbItem, PageProps, PaginatedData } from '@/types';
import DataTable from '@/components/DataTable';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { useState } from 'react';
import { useForm } from '@inertiajs/react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import AppLayout from '@/layouts/app-layout';

interface Departement {
    id: number;
    nom: string;
    code: string;
    villes?: unknown[];
}

export default function Index({ departements }: PageProps<{ departements: PaginatedData<Departement> }>) {
    const [isOpen, setIsOpen] = useState(false);
    const [editingDepartement, setEditingDepartement] = useState<Departement | null>(null);

    const form = useForm({
        nom: '',
        code: '',
    });

    const columns = [
        { key: 'nom', label: 'Nom' },
        { key: 'code', label: 'Code' },
        {
            key: 'villes',
            label: 'Villes',
            render: (value: unknown) => Array.isArray(value) ? value.length : 0,
        },
    ];

    const handleAdd = () => {
        setEditingDepartement(null);
        form.reset();
        setIsOpen(true);
    };

    const handleEdit = (row: Record<string, unknown>) => {
        const departement = row as { id: number; nom: string; code: string; villes?: unknown[] };
        setEditingDepartement(departement);
        form.setData({
            nom: departement.nom,
            code: departement.code,
        });
        setIsOpen(true);
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        if (editingDepartement) {
            form.put(route('admin.departements.update', editingDepartement.id), {
                onSuccess: () => setIsOpen(false),
            });
        } else {
            form.post(route('admin.departements.store'), {
                onSuccess: () => setIsOpen(false),
            });
        }
    };

    const handleDelete = (row: Record<string, unknown>) => {
        const departement = row as unknown as Departement;
        if (confirm('Êtes-vous sûr de vouloir supprimer ce département ?')) {
            router.delete(route('admin.departements.destroy', departement.id));
        }
    };
    const breadcrumbs: BreadcrumbItem[] = [
        {
            title: 'Départements',
            href: '/admin/departements',
        },
    ];
    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Départements" />

            <div className="flex h-full flex-1 flex-col gap-4 rounded-xl p-4">
                <DataTable
                    title="Départements"
                    columns={columns}
                    data={departements.data.map(departement => ({
                        id: departement.id,
                        nom: departement.nom,
                        code: departement.code,
                        villes: departement.villes,
                    }))}
                    onAdd={handleAdd}
                    onEdit={handleEdit}
                    onDelete={handleDelete}
                    pagination={{
                        links: departements.links,
                        from: departements.from,
                        to: departements.to,
                        total: departements.total
                    }}
                />
            </div>
            <Dialog open={isOpen} onOpenChange={setIsOpen}>
                <DialogContent>
                    <DialogHeader>
                        <DialogTitle>
                            {editingDepartement ? 'Modifier le département' : 'Ajouter un département'}
                        </DialogTitle>
                    </DialogHeader>
                    <form onSubmit={handleSubmit} className="space-y-4">
                        <div>
                            <Input
                                placeholder="Nom du département"
                                value={form.data.nom}
                                onChange={e => form.setData('nom', e.target.value)}
                            />
                        </div>
                        <div>
                            <Input
                                placeholder="Code du département"
                                value={form.data.code}
                                onChange={e => form.setData('code', e.target.value)}
                            />
                        </div>
                        <div className="flex justify-end space-x-2">
                            <Button
                                type="button"
                                variant="outline"
                                onClick={() => setIsOpen(false)}
                            >
                                Annuler
                            </Button>
                            <Button type="submit" disabled={form.processing}>
                                {editingDepartement ? 'Modifier' : 'Ajouter'}
                            </Button>
                        </div>
                    </form>
                </DialogContent>
            </Dialog>
        </AppLayout>
    );
}

