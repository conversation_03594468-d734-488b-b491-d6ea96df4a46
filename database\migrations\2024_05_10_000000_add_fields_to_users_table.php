<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->enum('civilite', ['Monsieur', 'Madame', 'Mademoiselle'])->nullable()->after('prenom');
            $table->date('date_naissance')->nullable()->after('civilite');
            $table->string('lieu_naissance')->nullable()->after('date_naissance');
            $table->string('ville')->nullable()->after('lieu_naissance');
            $table->string('code_postal', 5)->nullable()->after('ville');
            $table->string('adresse')->nullable()->after('code_postal');
            $table->string('mobile', 10)->nullable()->after('adresse');
            $table->string('tel', 10)->nullable()->after('mobile');
            $table->string('num_permis')->nullable()->after('tel');
            $table->date('date_permis')->nullable()->after('num_permis');
            $table->string('lieu_permis')->nullable()->after('date_permis');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn([
                'civilite',
                'date_naissance',
                'lieu_naissance',
                'ville',
                'code_postal',
                'adresse',
                'mobile',
                'tel',
                'num_permis',
                'date_permis',
                'lieu_permis'
            ]);
        });
    }
};
