<?php

namespace App\Http\Controllers;

use App\Models\Contact;
use App\Models\User;
use App\Notifications\NewContactNotification;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Notification;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Validator;

class ContactController extends Controller
{
    /**
     * Store a newly created contact message in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'nom' => 'required|string|max:255',
            'prenom' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'telephone' => 'nullable|string|max:20',
            'sujet' => 'required|in:stage,test,pssm,autre',
            'message' => 'required|string',
            'captcha' => 'required|string',
            'captcha_hash' => 'required|string',
        ], [
            'nom.required' => 'Le nom est obligatoire.',
            'prenom.required' => 'Le prénom est obligatoire.',
            'email.required' => 'L\'adresse email est obligatoire.',
            'email.email' => 'L\'adresse email doit être valide.',
            'sujet.required' => 'Le sujet est obligatoire.',
            'sujet.in' => 'Le sujet sélectionné n\'est pas valide.',
            'message.required' => 'Le message est obligatoire.',
            'captcha.required' => 'Le code de vérification est obligatoire.',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        // Vérifier le CAPTCHA
        if (!Hash::check(strtolower($request->captcha), $request->captcha_hash)) {
            return back()->withErrors(['captcha' => 'Le code de vérification est incorrect.'])->withInput();
        }

        // Créer le message de contact
        $contact = Contact::create([
            'nom' => $request->nom,
            'prenom' => $request->prenom,
            'email' => $request->email,
            'telephone' => $request->telephone,
            'sujet' => $request->sujet,
            'message' => $request->message,
            'lu' => false,
        ]);

        // Envoyer une notification aux administrateurs
        $admins = User::where('role', 'admin')->get();

        // Get admin email from config
        // $adminEmail = config('mail.admin_email', env('ADMIN_EMAIL', '<EMAIL>'));

        // // Create a temporary user object for the admin email
        // $adminUser = new User(['email' => $adminEmail]);

        Notification::send($admins, new NewContactNotification($contact));

        return redirect()->back()->with('success', 'Votre message a été envoyé avec succès. Nous vous répondrons dans les plus brefs délais.');
    }
}
