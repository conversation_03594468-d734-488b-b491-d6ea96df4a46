<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Reservation extends Model
{
    use HasFactory;

    protected $fillable = [
        'stage_id',
        'user_id',
        'type_stage_id',
        'date_reservation',
        'statut',
        'date_infraction',
        'heure_infraction',
        'lieu_infraction',
        'permis_recto',
        'permis_verso',
        'lettre_48n_recto',
        'lettre_48n_verso',
        'cas',
        'date_infr',
        'heure_infr',
        'lieu_infr',
        'recto_path',
        'verso_path',
        'recto48_path',
        'verso48_path',
        'date_paiement',
        'methode_paiement',
        'transaction_id'
    ];

    protected $casts = [
        'date_reservation' => 'datetime',
        'date_infraction' => 'date',
        'heure_infraction' => 'datetime',
        'date_infr' => 'date',
        'date_paiement' => 'datetime'
    ];

    public function stage()
    {
        return $this->belongsTo(Stage::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function typeStage()
    {
        return $this->belongsTo(TypeStage::class);
    }
}
