<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\TypeTestPsycho;
use Illuminate\Http\Request;
use Inertia\Inertia;

class TypeTestPsychoController extends Controller
{
    public function index()
    {
        return Inertia::render('Admin/TypesTestsPsychos/Index', [
            'types' => TypeTestPsycho::withCount('reservations')->paginate(10)
        ]);
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'nom' => 'required|string|max:255',
            'description' => 'nullable|string'
        ]);

        TypeTestPsycho::create($validated);

        return redirect()->back()->with('success', 'Type de test psychotechnique créé avec succès.');
    }

    public function update(Request $request, TypeTestPsycho $typesTestPsycho)
    {
        $validated = $request->validate([
            'nom' => 'required|string|max:255',
            'description' => 'nullable|string'
        ]);

        $typesTestPsycho->update($validated);

        return redirect()->back()->with('success', 'Type de test psychotechnique mis à jour avec succès.');
    }

    public function destroy(TypeTestPsycho $typesTestPsycho)
    {
        $typesTestPsycho->delete();
        return redirect()->back()->with('success', 'Type de test psychotechnique supprimé avec succès.');
    }
}
