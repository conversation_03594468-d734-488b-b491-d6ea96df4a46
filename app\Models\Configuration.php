<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Cache;

class Configuration extends Model
{
    protected $fillable = [
        'key',
        'value',
        'type',
        'description',
        'group',
    ];

    protected $casts = [
        'value' => 'string',
    ];

    /**
     * Get a configuration value by key
     */
    public static function get(string $key, $default = null)
    {
        $cacheKey = "config_{$key}";

        return Cache::remember($cacheKey, 3600, function () use ($key, $default) {
            $config = static::where('key', $key)->first();

            if (!$config) {
                return $default;
            }

            return static::castValue($config->value, $config->type);
        });
    }

    /**
     * Set a configuration value
     */
    public static function set(string $key, $value, string $type = 'string', ?string $description = null, string $group = 'general')
    {
        $config = static::updateOrCreate(
            ['key' => $key],
            [
                'value' => static::prepareValue($value, $type),
                'type' => $type,
                'description' => $description,
                'group' => $group,
            ]
        );

        // Clear cache
        Cache::forget("config_{$key}");

        return $config;
    }

    /**
     * Cast value to appropriate type
     */
    protected static function castValue($value, string $type)
    {
        switch ($type) {
            case 'boolean':
                return filter_var($value, FILTER_VALIDATE_BOOLEAN);
            case 'integer':
                return (int) $value;
            case 'json':
                return json_decode($value, true);
            default:
                return $value;
        }
    }

    /**
     * Prepare value for storage
     */
    protected static function prepareValue($value, string $type): string
    {
        switch ($type) {
            case 'boolean':
                return $value ? '1' : '0';
            case 'json':
                return json_encode($value);
            default:
                return (string) $value;
        }
    }

    /**
     * Get all payment method configurations
     */
    public static function getPaymentMethods(): array
    {
        $cacheKey = 'payment_methods_config';

        return Cache::remember($cacheKey, 3600, function () {
            $configs = static::where('group', 'payment_methods')->get();

            $paymentMethods = [];
            foreach ($configs as $config) {
                $paymentMethods[$config->key] = static::castValue($config->value, $config->type);
            }

            return $paymentMethods;
        });
    }

    /**
     * Clear all configuration cache
     */
    public static function clearCache()
    {
        Cache::forget('payment_methods_config');

        $configs = static::all();
        foreach ($configs as $config) {
            Cache::forget("config_{$config->key}");
        }
    }
}
