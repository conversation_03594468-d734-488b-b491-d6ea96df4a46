import { Link } from '@inertiajs/react';
import { ReactNode } from 'react';

import FrontLayout from './front-layout';

interface ClientLayoutProps {
  children: ReactNode;
  title?: string;
}

export default function ClientLayout({ children, title = 'Mon compte' }: ClientLayoutProps) {

  return (
        <FrontLayout title={title}>

        <div className="container mx-auto px-4 py-6">
          <div className="grid grid-cols-1 gap-8 md:grid-cols-[250px_1fr]">
            <div className="hidden md:block">
              <div className="space-y-1">
                <h3 className="font-medium text-lg mb-4">Mon compte</h3>
                <nav className="flex flex-col space-y-1">
                  <Link
                    href={route('client.dashboard')}
                    className={`px-3 py-2 rounded-md hover:bg-muted ${route().current('client.dashboard') ? 'bg-muted font-medium' : ''}`}
                  >
                    <PERSON>au de bord
                  </Link>
                  <Link
                    href={route('client.profile')}
                    className={`px-3 py-2 rounded-md hover:bg-muted ${route().current('client.profile') ? 'bg-muted font-medium' : ''}`}
                  >
                    Mon profil
                  </Link>
                  <Link
                    href={route('client.reservations')}
                    className={`px-3 py-2 rounded-md hover:bg-muted ${route().current('client.reservations') ? 'bg-muted font-medium' : ''}`}
                  >
                    Mes réservations
                  </Link>
                  <Link
                    href={route('client.stages')}
                    className={`px-3 py-2 rounded-md hover:bg-muted ${route().current('client.stages') ? 'bg-muted font-medium' : ''}`}
                  >
                    Mes stages
                  </Link>
                  <Link
                    href={route('client.tests')}
                    className={`px-3 py-2 rounded-md hover:bg-muted ${route().current('client.tests') ? 'bg-muted font-medium' : ''}`}
                  >
                    Mes tests psychotechniques
                  </Link>
                  <Link
                    href={route('client.reservation-tests')}
                    className={`px-3 py-2 rounded-md hover:bg-muted ${route().current('client.reservation-tests') ? 'bg-muted font-medium' : ''}`}
                  >
                    Réservations de tests
                  </Link>
                  <Link
                    href={route('client.documents')}
                    className={`px-3 py-2 rounded-md hover:bg-muted ${route().current('client.documents') ? 'bg-muted font-medium' : ''}`}
                  >
                    Mes documents
                  </Link>
                </nav>
              </div>
            </div>
            <div>
              {children}
            </div>
          </div>
        </div>

    </FrontLayout>
  );
}
