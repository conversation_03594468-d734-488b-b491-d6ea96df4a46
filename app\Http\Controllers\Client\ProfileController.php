<?php

namespace App\Http\Controllers\Client;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;

class ProfileController extends Controller
{
    public function index()
    {
        return Inertia::render('Client/Profile');
    }

    public function update(Request $request)
    {
        $validated = $request->validate([
            'civilite' => 'nullable|in:<PERSON>,<PERSON>,Mademoiselle',
            'nom' => 'required|string|max:255',
            'prenom' => 'required|string|max:255',
            'email' => 'required|email|max:255|unique:users,email,' . Auth::id(),
            'date_naissance' => 'nullable|date',
            'lieu_naissance' => 'nullable|string|max:255',
            'ville' => 'nullable|string|max:255',
            'code_postal' => 'nullable|string|max:5',
            'adresse' => 'nullable|string|max:255',
            'mobile' => 'nullable|string|max:10',
            'tel' => 'nullable|string|max:10',
            'num_permis' => 'nullable|string|max:255',
            'date_permis' => 'nullable|date',
            'lieu_permis' => 'nullable|string|max:255',
        ]);

        $user = Auth::user();
        $user->update($validated);

        return back()->with('success', 'Profil mis à jour avec succès.');
    }
}
