import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import ClientLayout from '@/layouts/client-layout';
import { type Reservation, type ReservationTestPsycho, type SharedData } from '@/types';
import { Head, Link, usePage } from '@inertiajs/react';
import { format, parseISO } from 'date-fns';
import { fr } from 'date-fns/locale';
import { AlertCircle, Brain, CalendarDays, CheckCircle, Clock, CreditCard, Package, XCircle } from 'lucide-react';

interface DashboardProps {
    stats: {
        reservationsEnCours: number;
        reservationsTestEnCours: number;
        stagesAVenir: number;
        testsCount: number;
        testsConfirmes: number;
        testsEnAttente: number;
        testsAnnules: number;
        testsAVenir: number;
        totalPaiements: number;
    };
    activitesRecentes: Reservation[];
    activitesRecentesTests: ReservationTestPsycho[];
    prochainsTests: ReservationTestPsycho[];
}

export default function Dashboard() {
    const { auth, stats, activitesRecentes, activitesRecentesTests } = usePage<SharedData & DashboardProps>().props;

    // Fonction pour obtenir l'icône et la couleur en fonction du statut
    const getStatusInfo = (statut: string) => {
        switch (statut) {
            case 'confirmée':
                return { icon: <CheckCircle className="h-4 w-4" />, color: 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300' };
            case 'en attente':
                return { icon: <Clock className="h-4 w-4" />, color: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300' };
            case 'annulée':
                return { icon: <XCircle className="h-4 w-4" />, color: 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300' };
            default:
                return { icon: <AlertCircle className="h-4 w-4" />, color: 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-300' };
        }
    };

    return (
        <ClientLayout title="Tableau de bord">
            <Head title="Tableau de bord" />

            <div className="space-y-6 ">
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3 justify-between ">
                    <div className="col-span-2 my-6">
                        <h1 className="text-2xl font-bold tracking-tight">Tableau de bord</h1>
                        <p className="text-muted-foreground">Bienvenue, {auth.user.prenom} ! Voici un aperçu de votre compte.</p>
                    </div>
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-2xl font-bold">{stats.totalPaiements.toFixed(2)} €</CardTitle>
                            <CreditCard className="text-muted-foreground h-4 w-4" />
                        </CardHeader>
                        <CardContent>
                            <p className="text-muted-foreground text-xs">Total des paiements</p>
                        </CardContent>
                    </Card>
                </div>

                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Réservations stages</CardTitle>
                            <Package className="text-muted-foreground h-4 w-4" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{stats.reservationsEnCours}</div>
                            <p className="text-muted-foreground text-xs">Réservations stages en cours</p>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Stages</CardTitle>
                            <CalendarDays className="text-muted-foreground h-4 w-4" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{stats.stagesAVenir}</div>
                            <p className="text-muted-foreground text-xs">Stages à venir</p>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Réservations Test</CardTitle>
                            <Package className="text-muted-foreground h-4 w-4" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{stats.reservationsTestEnCours}</div>
                            <p className="text-muted-foreground text-xs">Réservations test en cours</p>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Tests</CardTitle>
                            <Brain className="text-muted-foreground h-4 w-4" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{stats.testsAVenir}</div>
                            <p className="text-muted-foreground text-xs">Tests à venir</p>
                        </CardContent>
                    </Card>
                </div>

                <div className="grid gap-4">
                    <Card>
                        <CardHeader>
                            <CardTitle>Activité récente - Stages</CardTitle>
                            <CardDescription>Vos dernières réservations de stages</CardDescription>
                        </CardHeader>
                        <CardContent>
                            {activitesRecentes && activitesRecentes.length > 0 ? (
                                <div className="space-y-4">
                                    {activitesRecentes.map((reservation) => {
                                        const statusInfo = getStatusInfo(reservation.statut);
                                        return (
                                            <div key={reservation.id} className="flex items-start space-x-4 border-b pb-4 last:border-0 last:pb-0">
                                                <div className="flex-shrink-0">
                                                    <CalendarDays className="bg-primary/10 text-primary h-10 w-10 rounded-full p-2" />
                                                </div>
                                                <div className="flex-1 space-y-1">
                                                    <div className="flex items-center justify-between">
                                                        <p className="font-medium">
                                                            {reservation.stage && (
                                                                <>
                                                                    Stage du{' '}
                                                                    {format(parseISO(reservation.stage.date_debut), 'dd MMMM yyyy', { locale: fr })}
                                                                </>
                                                            )}
                                                        </p>
                                                        <Badge className={statusInfo.color}>
                                                            <span className="flex items-center gap-1">
                                                                {statusInfo.icon}
                                                                {reservation.statut}
                                                            </span>
                                                        </Badge>
                                                    </div>
                                                    <p className="text-muted-foreground text-sm">
                                                        {reservation.stage && reservation.stage.lieu && (
                                                            <>
                                                                {reservation.stage.lieu.nom}, {reservation.stage.lieu.ville?.nom}
                                                            </>
                                                        )}
                                                    </p>
                                                    <p className="text-muted-foreground text-xs">
                                                        Réservé le{' '}
                                                        {format(parseISO(reservation.date_reservation), 'dd/MM/yyyy à HH:mm', { locale: fr })}
                                                    </p>
                                                    <div className="pt-1">
                                                        <Link href={route('client.reservations')} className="text-primary text-xs hover:underline">
                                                            Voir les détails
                                                        </Link>
                                                    </div>
                                                </div>
                                            </div>
                                        );
                                    })}
                                </div>
                            ) : (
                                <div className="text-muted-foreground py-6 text-center">Aucune activité récente</div>
                            )}
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader>
                            <CardTitle>Activité récente - Tests</CardTitle>
                            <CardDescription>Vos dernières réservations de tests</CardDescription>
                        </CardHeader>
                        <CardContent>
                            {activitesRecentesTests && activitesRecentesTests.length > 0 ? (
                                <div className="space-y-4">
                                    {activitesRecentesTests.map((reservation) => {
                                        const statusInfo = getStatusInfo(reservation.statut);
                                        const testDate = reservation.test_psycho?.date ? parseISO(reservation.test_psycho.date) : new Date();

                                        return (
                                            <div key={reservation.id} className="flex items-start space-x-4 border-b pb-4 last:border-0 last:pb-0">
                                                <div className="flex-shrink-0">
                                                    <Brain className="bg-primary/10 text-primary h-10 w-10 rounded-full p-2" />
                                                </div>
                                                <div className="flex-1 space-y-1">
                                                    <div className="flex items-center justify-between">
                                                        <p className="font-medium">
                                                            {reservation.test_psycho && <>Test du {format(testDate, 'dd/MM/yyyy', { locale: fr })}</>}
                                                        </p>
                                                        <Badge className={statusInfo.color}>
                                                            <span className="flex items-center gap-1">
                                                                {statusInfo.icon}
                                                                {reservation.statut}
                                                            </span>
                                                        </Badge>
                                                    </div>
                                                    <p className="text-muted-foreground text-sm">
                                                        {reservation.type_test_psycho?.nom || 'Type non spécifié'}
                                                    </p>
                                                    <p className="text-muted-foreground text-xs">
                                                        Réservé le {format(parseISO(reservation.date_reservation), 'dd/MM/yyyy', { locale: fr })}
                                                    </p>
                                                    <div className="pt-1">
                                                        <Link
                                                            href={route('client.reservation-tests')}
                                                            className="text-primary text-xs hover:underline"
                                                        >
                                                            Voir les détails
                                                        </Link>
                                                    </div>
                                                </div>
                                            </div>
                                        );
                                    })}
                                </div>
                            ) : (
                                <div className="text-muted-foreground py-6 text-center">Aucune activité récente</div>
                            )}
                        </CardContent>
                    </Card>
                </div>

                <Card>
                    <CardHeader>
                        <CardTitle>Informations personnelles</CardTitle>
                        <CardDescription>Vos informations de base</CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-2">
                            <div className="flex justify-between">
                                <span className="text-muted-foreground">Nom</span>
                                <span className="font-medium">{auth.user.nom}</span>
                            </div>
                            <div className="flex justify-between">
                                <span className="text-muted-foreground">Prénom</span>
                                <span className="font-medium">{auth.user.prenom}</span>
                            </div>
                            <div className="flex justify-between">
                                <span className="text-muted-foreground">Email</span>
                                <span className="font-medium">{auth.user.email}</span>
                            </div>
                            {typeof auth.user.mobile === 'string' && auth.user.mobile && (
                                <div className="flex justify-between">
                                    <span className="text-muted-foreground">Mobile</span>
                                    <span className="font-medium">{auth.user.mobile}</span>
                                </div>
                            )}
                            <div className="pt-4">
                                <Link href={route('client.profile')} className="text-primary text-sm hover:underline">
                                    Modifier mon profil
                                </Link>
                            </div>
                        </div>
                    </CardContent>
                </Card>
            </div>
        </ClientLayout>
    );
}
