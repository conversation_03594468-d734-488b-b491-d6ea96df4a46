<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Departement;
use Illuminate\Http\Request;
use Inertia\Inertia;

class DepartementController extends Controller
{
    public function index()
    {
        return Inertia::render('Admin/Departements/Index', [
            'departements' => Departement::with('villes')->paginate(10)
        ]);
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'nom' => 'required|string|max:255',
            'code' => 'required|string|max:3|unique:departements'
        ]);

        Departement::create($validated);

        return redirect()->back()->with('success', 'Département créé avec succès.');
    }

    public function update(Request $request, Departement $departement)
    {
        $validated = $request->validate([
            'nom' => 'required|string|max:255',
            'code' => 'required|string|max:3|unique:departements,code,' . $departement->id
        ]);

        $departement->update($validated);

        return redirect()->back()->with('success', 'Département mis à jour avec succès.');
    }

    public function destroy(Departement $departement)
    {
        $departement->delete();
        return redirect()->back()->with('success', 'Département supprimé avec succès.');
    }
}