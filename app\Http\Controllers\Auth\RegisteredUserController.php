<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Auth\Events\Registered;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rules;
use Inertia\Inertia;
use Inertia\Response;

class RegisteredUserController extends Controller
{
    /**
     * Show the registration page.
     */
    public function create(): Response
    {
        return Inertia::render('auth/register');
    }

    /**
     * Handle an incoming registration request.
     *
     * @throws \Illuminate\Validation\ValidationException
     */
    public function store(Request $request): RedirectResponse
    {
        $request->validate([
            'civilite' => 'required|in:Monsieur,Madame,Mademoiselle',
            'nom' => 'required|string|max:255',
            'prenom' => 'required|string|max:255',
            'email' => 'required|string|lowercase|email|max:255|unique:'.User::class,
            'password' => ['required', 'confirmed', Rules\Password::defaults()],
            'date_naissance' => 'required|date',
            'lieu_naissance' => 'required|string|max:255',
            'ville' => 'required|string|max:255',
            'code_postal' => 'required|string|max:5',
            'adresse' => 'required|string|max:255',
            'mobile' => 'required|string|max:10',
            'tel' => 'nullable|string|max:10',
            'num_permis' => 'nullable|string|max:255',
            'date_permis' => 'nullable|date',
            'lieu_permis' => 'nullable|string|max:255',
        ]);

        $user = User::create([
            'civilite' => $request->civilite,
            'nom' => $request->nom,
            'prenom' => $request->prenom,
            'email' => $request->email,
            'password' => Hash::make($request->password),
            'role' => 'client',
            'date_naissance' => $request->date_naissance,
            'lieu_naissance' => $request->lieu_naissance,
            'ville' => $request->ville,
            'code_postal' => $request->code_postal,
            'adresse' => $request->adresse,
            'mobile' => $request->mobile,
            'tel' => $request->tel,
            'num_permis' => $request->num_permis,
            'date_permis' => $request->date_permis,
            'lieu_permis' => $request->lieu_permis,
        ]);

        event(new Registered($user));

        Auth::login($user);
// Vérifier s'il y a une URL de redirection après connexion
        if (session()->has('redirect_after_login')) {
            $redirectUrl = session('redirect_after_login');
            session()->forget('redirect_after_login');
            return redirect()->to($redirectUrl);
        }
        return to_route('client.dashboard');
    }
}
