<?php

namespace App\Http\Controllers;


use Gregwar\Captcha\CaptchaBuilder;
use Illuminate\Support\Facades\Hash;

class CaptchaController extends Controller
{
    public function generateCaptcha()
    {
        $builder = new CaptchaBuilder();
        $builder->build();

        $captcha_text = strtolower($builder->getPhrase());
        $hashed_captcha_text  = Hash::make($captcha_text);
        $imageData = base64_encode($builder->get());

        return response()->json([
            'captcha' => 'data:image/jpeg;base64,' . $imageData,
            'hashed_captcha_text' => $hashed_captcha_text,
            'captcha_text' => $captcha_text,
        ])->header('Access-Control-Allow-Origin', '*');
    }
}
