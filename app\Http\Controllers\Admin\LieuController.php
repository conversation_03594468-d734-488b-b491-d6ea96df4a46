<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Departement;
use App\Models\Lieu;
use App\Models\Ville;
use Illuminate\Http\Request;
use Inertia\Inertia;

class LieuController extends Controller
{
    public function index()
    {
        return Inertia::render('Admin/Lieux/Index', [
            'lieux' => Lieu::with(['ville.departement', 'stages'])->paginate(10),
            'villes' => Ville::get()->map(fn ($ville) => ['id' => $ville->id, 'nom' => $ville->nom, 'departement_id' => $ville->departement_id]),
            'departements' => Departement::get()->map(fn ($d) => ['id' => $d->id, 'nom' => $d->nom])
        ]);
    }
    public function store(Request $request)
    {
        $validated = $request->validate([
            'nom' => 'required|string|max:255',
            'adresse' => 'required|string|max:255',
            'ville_id' => 'required|exists:villes,id'
        ]);

        Lieu::create($validated);

        return redirect()->back()->with('success', 'Lieu créé avec succès.');
    }

    public function update(Request $request, Lieu $lieu)
    {
        $validated = $request->validate([
            'nom' => 'required|string|max:255',
            'adresse' => 'required|string|max:255',
            'ville_id' => 'required|exists:villes,id'
        ]);

        $lieu->update($validated);

        return redirect()->back()->with('success', 'Lieu mis à jour avec succès.');
    }

    public function destroy(Lieu $lieu)
    {
        $lieu->delete();
        return redirect()->back()->with('success', 'Lieu supprimé avec succès.');
    }
}



