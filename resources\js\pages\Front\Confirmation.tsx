import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import FrontLayout from '@/layouts/front-layout';
import { Head } from '@inertiajs/react';
import { format, parseISO } from 'date-fns';
import { fr } from 'date-fns/locale';
import { Calendar, CheckCircle2, MapPin, Printer, User } from 'lucide-react';

interface ConfirmationProps {
    reservation: {
        id: number;
        statut: string;
        date_reservation: string;
        date_paiement: string;
        methode_paiement?: string;
        cas?: string;
        type_stage?: {
            id: number;
            nom: string;
            description?: string;
        };
        stage: {
            id: number;
            reference: string;
            date_debut: string;
            date_fin: string;
            prix: number;
            lieu: {
                nom: string;
                adresse: string;
                ville: {
                    nom: string;
                    departement: {
                        nom: string;
                    };
                };
            };
        };
        user: {
            id: number;
            nom: string;
            prenom: string;
            email: string;
        };
    };
}

export default function Confirmation({ reservation }: ConfirmationProps) {
    // Fonction pour imprimer la confirmation
    const handlePrint = () => {
        // Ouvrir une nouvelle fenêtre pour l'impression
        const printWindow = window.open('', '_blank');

        if (!printWindow) {
            alert('Veuillez autoriser les fenêtres pop-up pour imprimer la confirmation.');
            return;
        }

        // Formater les dates pour l'impression
        const dateDebut = format(parseISO(reservation.stage.date_debut), 'EEEE dd MMMM yyyy', { locale: fr });
        const dateFin = format(parseISO(reservation.stage.date_fin), 'EEEE dd MMMM yyyy', { locale: fr });
        const datePaiement = reservation.date_paiement
            ? format(parseISO(reservation.date_paiement), 'dd/MM/yyyy à HH:mm', { locale: fr })
            : 'Non spécifiée';

        // Déterminer le type de stage
        let typeStage = '';
        if (reservation.type_stage && reservation.type_stage.nom) {
            typeStage = reservation.type_stage.nom;
        } else if (reservation.cas === '1') {
            typeStage = 'Récupération volontaire de 4 points';
        } else if (reservation.cas === '2') {
            typeStage = 'Stage en période probatoire';
        } else if (reservation.cas === '3') {
            typeStage = 'Alternative aux poursuites ou composition pénale';
        } else if (reservation.cas === '4') {
            typeStage = "Peine complémentaire ou mise à l'épreuve";
        } else {
            typeStage = 'Stage de récupération de points';
        }

        // Déterminer la méthode de paiement
        let methodePaiement = '';
        if (reservation.methode_paiement === 'card') methodePaiement = 'Carte bancaire';
        else if (reservation.methode_paiement === 'paypal') methodePaiement = 'PayPal';
        else if (reservation.methode_paiement === 'virement') methodePaiement = 'Virement bancaire';
        else if (reservation.methode_paiement === 'cheque') methodePaiement = 'Chèque';
        else if (reservation.methode_paiement === 'bon') methodePaiement = 'Bon';
        else methodePaiement = 'Non spécifiée';

        // Créer le contenu HTML à imprimer
        const printContent = `
      <!DOCTYPE html>
      <html>
      <head>
        <title>Confirmation de réservation - ${reservation.stage.reference}</title>
        <meta charset="utf-8">
        <style>
          body {
            font-family: Arial, sans-serif;
            line-height: 1.5;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 15px;
            font-size: .8rem;
          }
          .header {
            text-align: center;
            margin-bottom: 20px;
            border-bottom: 1px solid #ddd;
            padding-bottom: 10px;
          }
          .logo {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 10px;
          }
          h1 {
            font-size: 22px;
            margin-bottom: 20px;
            text-align: center;
          }
          .confirmation-box {
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 20px;
            margin-bottom: 30px;
          }
          .section {
            margin-bottom: 10px;
          }
          .section-title {
            font-weight: bold;
            margin-bottom: 5px;
            font-size: 14px;
            color: #4F46E5;
          }
          .info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
          }
          .info-label {
            font-weight: bold;
            margin-right: 10px;
          }
          .important-info {
            background-color: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            margin-top: 20px;
          }
          .important-info ul {
            margin-top: 10px;
            padding-left: 20px;
          }
          .footer {
            margin-top: 20px;
            font-size: 12px;
            text-align: center;
            color: #666;
          }
          .status-badge {
            display: inline-block;
            background-color: #10B981;
            color: white;
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
          }
          .status-pending {
            background-color: #F59E0B;
          }
          .payment-instructions {
            margin-top: 15px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #f8f9fa;
          }
          .instruction-title {
            font-weight: bold;
            margin-bottom: 8px;
            color: #4F46E5;
          }
          .payment-instructions p {
            margin-bottom: 5px;
          }
          .payment-instructions ul {
            margin: 8px 0;
            padding-left: 20px;
          }
          @media print {
            body {
              print-color-adjust: exact;
              -webkit-print-color-adjust: exact;
            }
            .no-print {
              display: none;
            }
          }
        </style>
      </head>
      <body>

        <h1>Votre réservation est confirmée</h1>

        <div class="confirmation-box">
          <div class="section">
            <div class="section-title">Référence de réservation</div>
            <div>${reservation.stage.reference}</div>
          </div>

          <div class="section">
            <div class="section-title">Dates du stage</div>
            <div>Du ${dateDebut}</div>
            <div>Au ${dateFin}</div>
          </div>

          <div class="section">
            <div class="section-title">Lieu du stage</div>
            <div>${reservation.stage.lieu.nom}</div>
            <div>${reservation.stage.lieu.adresse || 'Adresse non spécifiée'}</div>
            <div>${reservation.stage.lieu.ville.nom}, ${reservation.stage.lieu.ville.departement.nom}</div>
          </div>

          <div class="section">
            <div class="section-title">Informations personnelles</div>
            <div>${reservation.user.prenom} ${reservation.user.nom}</div>
            <div>${reservation.user.email}</div>
          </div>

          <div class="section">
            <div class="section-title">Type de stage</div>
            <div>${typeStage}</div>
          </div>

          <div class="section">
            <div class="section-title">Détails du paiement</div>
            <div class="info-row">
              <span class="info-label">Montant:</span>
              <span>${reservation.stage.prix} €</span>
            </div>
            ${
                reservation.statut === 'confirmée'
                    ? `
            <div class="info-row">
              <span class="info-label">Date de paiement:</span>
              <span>${datePaiement}</span>
            </div>
            <div class="info-row">
              <span class="info-label">Statut:</span>
              <span class="status-badge">Payé</span>
            </div>
            <div class="info-row">
              <span class="info-label">Méthode de paiement:</span>
              <span>${methodePaiement}</span>
            </div>
            `
                    : `
            <div class="info-row">
              <span class="info-label">Statut:</span>
              <span class="status-badge status-pending">En attente de paiement</span>
            </div>
            ${
                reservation.methode_paiement
                    ? `
            <div class="info-row">
              <span class="info-label">Méthode de paiement:</span>
              <span>${methodePaiement}</span>
            </div>
            `
                    : ''
            }

            ${
                reservation.methode_paiement === 'virement'
                    ? `
            <div class="payment-instructions">
              <p class="instruction-title">Instructions pour le virement bancaire:</p>
              <p>Veuillez effectuer votre virement aux coordonnées suivantes:</p>
              <ul>
                <li>IBAN: FR76 XXXX XXXX XXXX XXXX XXXX XXX</li>
                <li>BIC: XXXXXXXX</li>
                <li>Bénéficiaire: Stage Permis</li>
                <li>Référence: ${reservation.stage.reference}</li>
              </ul>
              <p>Votre réservation sera confirmée dès réception du paiement.</p>
            </div>
            `
                    : ''
            }

            ${
                reservation.methode_paiement === 'cheque'
                    ? `
            <div class="payment-instructions">
                <p class="instruction-title">Instructions pour le paiement par chèque:</p>
                <p>Veuillez envoyer votre chèque à l'ordre de "Stage Permis" à l'adresse suivante:<br>
                Stage Permis, Palais Vauban 12 av Jean Moulin, 83000 TOULON<br>
                N'oubliez pas d'indiquer la référence ${reservation.stage.reference} au dos du chèque.<br>
                Votre réservation sera confirmée dès réception du paiement.</p>
            </div>
            `
                    : ''
            }

            ${
                reservation.methode_paiement === 'bon'
                    ? `
            <div class="payment-instructions">
              <p class="instruction-title">Instructions pour le paiement par bon:</p>
              <p>Veuillez envoyer votre bon de paiement à l'adresse suivante: <br>
              Stage Permis, Palais Vauban 12 av Jean Moulin, 83000 TOULON<br>
              Ou par email à: <EMAIL><br>
              N'oubliez pas d'indiquer la référence ${reservation.stage.reference}.<br>
              Votre réservation sera confirmée après vérification du bon.</p>
            </div>
            `
                    : ''
            }
            `
            }
          </div>

          <div class="important-info">
            <div class="section-title">Informations importantes</div>
            <ul>
              <li>Veuillez vous présenter 15 minutes avant le début du stage</li>
              <li>N'oubliez pas d'apporter une pièce d'identité et votre permis de conduire (si vous en êtes encore titulaire)</li>
              <li>La présence est obligatoire pendant toute la durée du stage (2 jours)</li>
              <li>Une attestation vous sera délivrée à la fin du stage</li>
              <li>Les points seront crédités environ 1 mois après le stage</li>
            </ul>
          </div>
        </div>

        <div class="footer">
          <p>Cette confirmation a été générée le ${format(new Date(), 'dd/MM/yyyy à HH:mm', { locale: fr })}</p>
          <p>Pour toute question, veuillez nous contacter par email ou par téléphone.</p>
        </div>

        <div class="no-print" style="text-align: center; margin-top: 30px;">
          <button onclick="window.print();" style="padding: 10px 20px; background-color: #4F46E5; color: white; border: none; border-radius: 5px; cursor: pointer;">
            Imprimer
          </button>
          <button onclick="window.close();" style="padding: 10px 20px; background-color: #6B7280; color: white; border: none; border-radius: 5px; cursor: pointer; margin-left: 10px;">
            Fermer
          </button>
        </div>

        <script>
          // Auto-focus the window when it loads
          window.onload = function() {
            window.focus();
          }
        </script>
      </body>
      </html>
    `;

        // Écrire le contenu dans la nouvelle fenêtre
        printWindow.document.open();
        printWindow.document.write(printContent);
        printWindow.document.close();

        // Déclencher l'impression après le chargement de la page
        printWindow.onload = function () {
            // Délai court pour s'assurer que les styles sont chargés
            setTimeout(() => {
                printWindow.focus();
                // L'utilisateur peut maintenant imprimer via le bouton dans la fenêtre
                // Ou décommenter la ligne suivante pour déclencher l'impression automatiquement
                // printWindow.print();
            }, 500);
        };

        // Fallback au cas où l'événement onload ne se déclenche pas
        setTimeout(() => {
            printWindow.focus();
        }, 1000);
    };
    return (
        <FrontLayout title="Confirmation de réservation">
            <Head title="Confirmation de réservation" />
            <div className="container mx-auto px-4 py-8">
                <div className="mb-8 flex items-center">
                    <div className="flex items-center gap-2">
                        <div className="flex h-8 w-8 items-center justify-center rounded-full bg-green-500 text-white">
                            <span className="text-sm">✓</span>
                        </div>
                        <span className="font-medium">Authentification</span>
                    </div>
                    <div className="mx-2 h-px w-8 bg-green-500"></div>
                    <div className="flex items-center gap-2">
                        <div className="flex h-8 w-8 items-center justify-center rounded-full bg-green-500 text-white">
                            <span className="text-sm">✓</span>
                        </div>
                        <span className="font-medium">Paiement</span>
                    </div>
                    <div className="mx-2 h-px w-8 bg-green-500"></div>
                    <div className="flex items-center gap-2">
                        <div className="flex h-8 w-8 items-center justify-center rounded-full bg-green-500 text-white">
                            <span className="text-sm">✓</span>
                        </div>
                        <span className="font-medium">Confirmation</span>
                    </div>
                </div>

                <div className="mb-8 flex flex-col items-center justify-center text-center">
                    <CheckCircle2 className="mb-4 h-16 w-16 text-green-500" />
                    <h1 className="mb-2 text-3xl font-bold">Réservation confirmée</h1>
                    <p className="text-muted-foreground">Votre réservation a été confirmée et votre place est réservée.</p>
                </div>

                <Card className="mx-auto max-w-3xl">
                    <CardHeader>
                        <CardTitle>Détails de votre réservation</CardTitle>
                        <CardDescription>Référence: {reservation.stage.reference}</CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-6">
                        <div className="bg-muted rounded-md p-4">
                            <div className="flex items-center gap-2">
                                <Calendar className="text-primary h-5 w-5" />
                                <div>
                                    <p className="font-medium">
                                        {format(parseISO(reservation.stage.date_debut), 'EEEE dd MMMM yyyy', { locale: fr })}
                                    </p>
                                    <p className="text-muted-foreground text-sm">
                                        au {format(parseISO(reservation.stage.date_fin), 'EEEE dd MMMM yyyy', { locale: fr })}
                                    </p>
                                </div>
                            </div>
                        </div>

                        <div className="grid gap-6 md:grid-cols-2">
                            <div>
                                <h3 className="mb-2 font-semibold">Lieu du stage</h3>
                                <div className="flex items-start gap-2">
                                    <MapPin className="text-muted-foreground mt-0.5 h-5 w-5" />
                                    <div>
                                        <p className="font-medium">{reservation.stage.lieu.nom}</p>
                                        <p className="text-muted-foreground text-sm">{reservation.stage.lieu.adresse || 'Adresse non spécifiée'}</p>
                                        <p className="text-muted-foreground text-sm">
                                            {reservation.stage.lieu.ville.nom}, {reservation.stage.lieu.ville.departement.nom}
                                        </p>
                                    </div>
                                </div>
                            </div>

                            <div>
                                <h3 className="mb-2 font-semibold">Informations personnelles</h3>
                                <div className="flex items-start gap-2">
                                    <User className="text-muted-foreground mt-0.5 h-5 w-5" />
                                    <div>
                                        <p className="font-medium">
                                            {reservation.user.prenom} {reservation.user.nom}
                                        </p>
                                        <p className="text-muted-foreground text-sm">{reservation.user.email}</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <Separator />

                        <div className="grid gap-6 md:grid-cols-2">
                            <div>
                                <h3 className="mb-2 font-semibold">Type de stage</h3>
                                <p>
                                    {reservation.type_stage ? (
                                        reservation.type_stage.nom
                                    ) : (
                                        <>
                                            {reservation.cas === '1' && 'Récupération volontaire de 4 points'}
                                            {reservation.cas === '2' && 'Stage en période probatoire'}
                                            {reservation.cas === '3' && 'Alternative aux poursuites ou composition pénale'}
                                            {reservation.cas === '4' && "Peine complémentaire ou mise à l'épreuve"}
                                            {!reservation.cas && 'Stage de récupération de points'}
                                        </>
                                    )}
                                </p>
                            </div>

                            <div>
                                <h3 className="mb-2 font-semibold">Paiement</h3>
                                <div className="flex items-center justify-between">
                                    <span>Montant:</span>
                                    <span className="font-medium">{reservation.stage.prix} €</span>
                                </div>

                                {/* Affichage différent selon le statut et la méthode de paiement */}
                                {reservation.statut === 'confirmée' ? (
                                    <>
                                        {/* Paiement confirmé */}
                                        <div className="flex items-center justify-between">
                                            <span>Date de paiement:</span>
                                            <span className="text-sm">
                                                {reservation.date_paiement &&
                                                    format(parseISO(reservation.date_paiement), 'dd/MM/yyyy à HH:mm', { locale: fr })}
                                            </span>
                                        </div>
                                        <div className="flex items-center justify-between">
                                            <span>Statut:</span>
                                            <span className="rounded-full bg-green-100 px-2 py-1 text-xs font-medium text-green-800">Payé</span>
                                        </div>
                                        <div className="flex items-center justify-between">
                                            <span>Méthode de paiement:</span>
                                            <span className="text-sm">
                                                {reservation.methode_paiement === 'paypal' && 'PayPal'}
                                                {reservation.methode_paiement === 'virement' && 'Virement bancaire'}
                                                {reservation.methode_paiement === 'cheque' && 'Chèque'}
                                                {reservation.methode_paiement === 'bon' && 'Bon'}
                                                {!reservation.methode_paiement && 'Non spécifiée'}
                                            </span>
                                        </div>
                                    </>
                                ) : (
                                    <>
                                        {/* Paiement en attente */}
                                        <div className="flex items-center justify-between">
                                            <span>Statut:</span>
                                            <span className="rounded-full bg-yellow-100 px-2 py-1 text-xs font-medium text-yellow-800">
                                                En attente de paiement
                                            </span>
                                        </div>
                                    </>
                                )}
                            </div>
                        </div>

                        <div>
                            {/* Affichage différent selon le statut et la méthode de paiement */}
                            {reservation.statut === 'confirmée' ? (
                                <></>
                            ) : (
                                <>
                                    {/* Instructions spécifiques selon la méthode de paiement */}
                                    {reservation.methode_paiement === 'virement' && (
                                        <div className="mt-2 rounded-md bg-blue-50 p-3 text-sm text-blue-800 dark:bg-blue-900/20 dark:text-blue-300">
                                            <p className="font-semibold">Instructions pour le virement bancaire:</p>
                                            <p className="mt-1">Veuillez effectuer votre virement aux coordonnées suivantes:</p>
                                            <ul className="mt-1 list-inside list-disc">
                                                <li>IBAN: FR76 XXXX XXXX XXXX XXXX XXXX XXX</li>
                                                <li>BIC: XXXXXXXX</li>
                                                <li>Bénéficiaire: Stage Permis</li>
                                                <li>Référence: {reservation.stage.reference}</li>
                                            </ul>
                                            <p className="mt-1">Votre réservation sera confirmée dès réception du paiement.</p>
                                        </div>
                                    )}

                                    {reservation.methode_paiement === 'cheque' && (
                                        <div className="mt-2 rounded-md bg-blue-50 p-3 text-sm text-blue-800 dark:bg-blue-900/20 dark:text-blue-300">
                                            <p className="font-semibold">Instructions pour le paiement par chèque:</p>
                                            <p className="mt-1">Veuillez envoyer votre chèque à l'ordre de "Stage Permis" à l'adresse suivante:</p>
                                            <p className="mt-1">
                                                Stage Permis
                                                <br />
                                                Palais Vauban 12 av Jean Moulin
                                                <br />
                                                83000 TOULON
                                            </p>
                                            <p className="mt-1">
                                                N'oubliez pas d'indiquer la référence {reservation.stage.reference} au dos du chèque.
                                            </p>
                                            <p className="mt-1">Votre réservation sera confirmée dès réception du paiement.</p>
                                        </div>
                                    )}

                                    {reservation.methode_paiement === 'bon' && (
                                        <div className="mt-2 rounded-md bg-blue-50 p-3 text-sm text-blue-800 dark:bg-blue-900/20 dark:text-blue-300">
                                            <p className="font-semibold">Instructions pour le paiement par bon:</p>
                                            <p className="mt-1">Veuillez envoyer votre bon de paiement à l'adresse suivante:</p>
                                            <p className="mt-1">
                                                Stage Permis
                                                <br />
                                                Palais Vauban 12 av Jean Moulin
                                                <br />
                                                83000 TOULON
                                            </p>
                                            <p className="mt-1">Ou par email à: <EMAIL></p>
                                            <p className="mt-1">N'oubliez pas d'indiquer la référence {reservation.stage.reference}.</p>
                                            <p className="mt-1">Votre réservation sera confirmée après vérification du bon.</p>
                                        </div>
                                    )}

                                    {reservation.methode_paiement === 'paypal' && (
                                        <div className="mt-2 rounded-md bg-blue-50 p-3 text-sm text-blue-800 dark:bg-blue-900/20 dark:text-blue-300">
                                            <p className="font-semibold">Paiement PayPal en cours:</p>
                                            <p className="mt-1">Votre paiement PayPal est en cours de traitement.</p>
                                            <p className="mt-1">
                                                Si vous avez quitté la page de paiement PayPal, veuillez contacter notre service client.
                                            </p>
                                        </div>
                                    )}

                                    {!reservation.methode_paiement && (
                                        <div className="mt-2 rounded-md bg-blue-50 p-3 text-sm text-blue-800 dark:bg-blue-900/20 dark:text-blue-300">
                                            <p className="font-semibold">Méthode de paiement non spécifiée</p>
                                            <p className="mt-1">Veuillez contacter notre service client pour finaliser votre paiement.</p>
                                        </div>
                                    )}
                                </>
                            )}
                        </div>
                        <div className="rounded-md bg-blue-50 p-4 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300">
                            <h3 className="mb-2 font-semibold">Informations importantes</h3>
                            <ul className="list-inside list-disc space-y-1 text-sm">
                                <li>Veuillez vous présenter 15 minutes avant le début du stage</li>
                                <li>N'oubliez pas d'apporter une pièce d'identité et votre permis de conduire (si vous en êtes encore titulaire)</li>
                                <li>La présence est obligatoire pendant toute la durée du stage (2 jours)</li>
                                <li>Une attestation vous sera délivrée à la fin du stage</li>
                                <li>Les points seront crédités environ 1 mois après le stage</li>
                            </ul>
                        </div>
                    </CardContent>
                    <CardFooter className="flex justify-between">
                        <Button variant="outline" asChild>
                            <a href={route('client.reservations')}>Mes réservations</a>
                        </Button>
                        <Button className="gap-2" onClick={handlePrint}>
                            <Printer className="h-4 w-4" />
                            Imprimer la confirmation
                        </Button>
                    </CardFooter>
                </Card>
            </div>
        </FrontLayout>
    );
}
