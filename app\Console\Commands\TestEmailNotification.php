<?php

namespace App\Console\Commands;

use App\Models\Reservation;
use App\Models\ReservationTestPsycho;
use App\Services\EmailNotificationService;
use Illuminate\Console\Command;

class TestEmailNotification extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'email:test-notification 
                            {type : Type of notification (stage|test)} 
                            {--id= : Specific reservation ID to test}
                            {--email= : Email address to send test to (default: admin email)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test email notifications for reservations';

    /**
     * Email notification service
     *
     * @var EmailNotificationService
     */
    protected $emailService;

    /**
     * Create a new command instance.
     */
    public function __construct(EmailNotificationService $emailService)
    {
        parent::__construct();
        $this->emailService = $emailService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $type = $this->argument('type');
        $reservationId = $this->option('id');
        $email = $this->option('email');

        if (!in_array($type, ['stage', 'test'])) {
            $this->error('Type must be either "stage" or "test"');
            return 1;
        }

        try {
            if ($type === 'stage') {
                $this->testStageNotification($reservationId, $email);
            } else {
                $this->testTestNotification($reservationId, $email);
            }

            $this->info('Email notification test completed successfully!');
            return 0;
        } catch (\Exception $e) {
            $this->error('Failed to send test email: ' . $e->getMessage());
            return 1;
        }
    }

    /**
     * Test stage reservation notification
     */
    private function testStageNotification($reservationId = null, $email = null)
    {
        if ($reservationId) {
            $reservation = Reservation::with(['stage.lieu.ville.departement', 'user', 'typeStage'])
                ->findOrFail($reservationId);
        } else {
            $reservation = Reservation::with(['stage.lieu.ville.departement', 'user', 'typeStage'])
                ->where('statut', 'confirmée')
                ->latest()
                ->first();

            if (!$reservation) {
                $this->warn('No confirmed stage reservations found. Creating a test scenario...');
                $reservation = Reservation::with(['stage.lieu.ville.departement', 'user', 'typeStage'])
                    ->latest()
                    ->first();
            }
        }

        if (!$reservation) {
            $this->error('No stage reservations found in the database.');
            return;
        }

        $this->info("Testing stage reservation notification for reservation #{$reservation->id}");
        $this->info("User: {$reservation->user->nom} {$reservation->user->prenom}");
        $this->info("Email: {$reservation->user->email}");

        if ($email) {
            $success = $this->emailService->sendDirectNotification($email, $reservation, 'stage');
            $this->info("Email sent to: {$email}");
        } else {
            $success = $this->emailService->sendStageReservationNotification($reservation);
            $adminEmail = config('mail.admin_email', env('ADMIN_EMAIL', '<EMAIL>'));
            $this->info("Email sent to admin: {$adminEmail}");
        }

        if ($success) {
            $this->info('✅ Email sent successfully!');
        } else {
            $this->error('❌ Failed to send email. Check logs for details.');
        }
    }

    /**
     * Test test reservation notification
     */
    private function testTestNotification($reservationId = null, $email = null)
    {
        if ($reservationId) {
            $reservation = ReservationTestPsycho::with(['testPsycho.lieu.ville.departement', 'user', 'typeTestPsycho'])
                ->findOrFail($reservationId);
        } else {
            $reservation = ReservationTestPsycho::with(['testPsycho.lieu.ville.departement', 'user', 'typeTestPsycho'])
                ->where('statut', 'confirmée')
                ->latest()
                ->first();

            if (!$reservation) {
                $this->warn('No confirmed test reservations found. Creating a test scenario...');
                $reservation = ReservationTestPsycho::with(['testPsycho.lieu.ville.departement', 'user', 'typeTestPsycho'])
                    ->latest()
                    ->first();
            }
        }

        if (!$reservation) {
            $this->error('No test reservations found in the database.');
            return;
        }

        $this->info("Testing test reservation notification for reservation #{$reservation->id}");
        $this->info("User: {$reservation->user->nom} {$reservation->user->prenom}");
        $this->info("Email: {$reservation->user->email}");

        if ($email) {
            $success = $this->emailService->sendDirectNotification($email, $reservation, 'test');
            $this->info("Email sent to: {$email}");
        } else {
            $success = $this->emailService->sendTestReservationNotification($reservation);
            $adminEmail = config('mail.admin_email', env('ADMIN_EMAIL', '<EMAIL>'));
            $this->info("Email sent to admin: {$adminEmail}");
        }

        if ($success) {
            $this->info('✅ Email sent successfully!');
        } else {
            $this->error('❌ Failed to send email. Check logs for details.');
        }
    }
}
