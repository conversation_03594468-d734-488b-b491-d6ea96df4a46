<?php

use App\Http\Controllers\Admin\ClientController;
use App\Http\Controllers\Admin\ConfigurationController;
use App\Http\Controllers\Admin\ContactController as AdminContactController;
use App\Http\Controllers\Admin\DashboardController;
use App\Http\Controllers\Admin\DepartementController;
use App\Http\Controllers\Admin\VilleController;
use App\Http\Controllers\Admin\LieuController;
use App\Http\Controllers\Admin\StageController;
use App\Http\Controllers\Admin\TypeStageController;
use App\Http\Controllers\Admin\ReservationController as AdminReservationController;
use App\Http\Controllers\Admin\TestPsychoController;
use App\Http\Controllers\Admin\TypeTestPsychoController;
use App\Http\Controllers\Admin\ReservationTestPsychoController;
use App\Http\Controllers\ContactController;
use App\Http\Controllers\FrontController;
use App\Http\Controllers\PaiementController;
use App\Http\Controllers\ReservationController;
use Illuminate\Support\Facades\Route;

// Routes front-end
Route::get('/', [FrontController::class, 'welcome'])->name('home');
Route::get('/permis-points', [FrontController::class, 'permisPoints'])->name('permis-points');
Route::get('/sensibilisation-accidents', [FrontController::class, 'sensibilisationAccidents'])->name('sensibilisation-accidents');
Route::get('/stage-france-permis', [FrontController::class, 'stageFrancePermis'])->name('stage-france-permis');
Route::get('/conditions-generales', [FrontController::class, 'conditionsGenerales'])->name('conditions-generales');
Route::get('/permis-en-danger', [FrontController::class, 'permisEnDanger'])->name('permis-en-danger');
Route::get('/antai', [FrontController::class, 'antai'])->name('antai');
Route::get('/stages', [FrontController::class, 'stages'])->name('stages');
Route::get('/tests-psychotechniques', [FrontController::class, 'testsPsychotechniques'])->name('tests-psychotechniques');
Route::get('/pssm', [FrontController::class, 'pssm'])->name('pssm');
Route::get('/dossiers', [FrontController::class, 'dossiers'])->name('dossiers');
Route::get('/contact', [FrontController::class, 'contact'])->name('contact');
Route::post('/contact', [ContactController::class, 'store'])->name('contact.store');

// Route pour le téléchargement de fichiers sécurisés
use App\Http\Controllers\FileController;
Route::get('/files/{path}', [FileController::class, 'download'])->name('files.download')->where('path', '.*');

// Routes de réservation pour stages
Route::get('/reservation/{stageId}', [ReservationController::class, 'show'])->name('reservation.show');
Route::post('/reservation', [ReservationController::class, 'store'])->name('reservation.store');

// Routes de réservation pour tests psychotechniques
use App\Http\Controllers\ReservationTestController;

Route::get('/reservation-test/{testId}', [ReservationTestController::class, 'show'])->name('reservation-test.show');
Route::post('/reservation-test', [ReservationTestController::class, 'store'])->name('reservation-test.store');

// Routes de paiement pour stages
Route::get('/paiement/{reservationId}', [PaiementController::class, 'show'])->name('paiement');
Route::get('/confirmation/{reservationId}', [PaiementController::class, 'confirmation'])->name('reservation.confirmation');

// Routes de paiement pour tests psychotechniques
Route::get('/paiement-test/{reservationId}', [PaiementController::class, 'showTest'])->name('paiement-test');
Route::get('/confirmation-test/{reservationId}', [PaiementController::class, 'confirmationTest'])->name('reservation-test.confirmation');

// Routes PayPal et SumUp
Route::middleware(['paypal.cors'])->group(function () {
    // Handle OPTIONS preflight requests for all PayPal routes
    Route::options('/paypal/{any}', function () {
        return response('', 200)
            ->header('Access-Control-Allow-Origin', '*')
            ->header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
            ->header('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With, X-CSRF-TOKEN')
            ->header('Access-Control-Allow-Credentials', 'true')
            ->header('Access-Control-Max-Age', '86400');
    })->where('any', '.*');

    Route::options('/sumup/{any}', function () {
        return response('', 200)
            ->header('Access-Control-Allow-Origin', '*')
            ->header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
            ->header('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With, X-CSRF-TOKEN')
            ->header('Access-Control-Allow-Credentials', 'true')
            ->header('Access-Control-Max-Age', '86400');
    })->where('any', '.*');

    Route::options('/paiement/{any}', function () {
        return response('', 200)
            ->header('Access-Control-Allow-Origin', '*')
            ->header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
            ->header('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With, X-CSRF-TOKEN')
            ->header('Access-Control-Allow-Credentials', 'true')
            ->header('Access-Control-Max-Age', '86400');
    })->where('any', '.*');

    // Regular PayPal routes for stages
    Route::get('/paypal/success/{reservationId}', [PaiementController::class, 'paypalSuccess'])->name('paypal.success');
    Route::get('/paypal/cancel/{reservationId}', [PaiementController::class, 'paypalCancel'])->name('paypal.cancel');
    Route::post('/paypal/webhook', [PaiementController::class, 'paypalWebhook'])->name('paypal.webhook');

    // PayPal routes for tests psychotechniques
    Route::get('/paypal/success-test/{reservationId}', [PaiementController::class, 'paypalSuccessTest'])->name('paypal.success.test');
    Route::get('/paypal/cancel-test/{reservationId}', [PaiementController::class, 'paypalCancelTest'])->name('paypal.cancel.test');

    // SumUp routes for stages
    Route::get('/sumup/success/{reservationId}', [PaiementController::class, 'sumupSuccess'])->name('sumup.success');

    // SumUp routes for tests psychotechniques
    Route::get('/sumup/success-test/{reservationId}', [PaiementController::class, 'sumupSuccessTest'])->name('sumup.success.test');

    // Payment processing routes
    Route::post('/paiement/{reservationId}', [PaiementController::class, 'process'])->name('paiement.process');
    Route::post('/paiement-test/{reservationId}', [PaiementController::class, 'processTest'])->name('paiement-test.process');
});


// Routes administrateur
Route::middleware(['auth', 'verified', 'role:admin'])->prefix('admin')->name('admin.')->group(function () {

    // dashboard
    Route::get('dashboard', [DashboardController::class, 'index'])->name('dashboard');
    Route::get('', [DashboardController::class, 'index']);
    // Départements
    Route::resource('departements', DepartementController::class);

    // Villes
    Route::resource('villes', VilleController::class);

    // Lieux
    Route::resource('lieux', LieuController::class);

    // Stages
    Route::get('stages/archive', [StageController::class, 'archive'])->name('stages.archive');
    Route::post('stages/import', [StageController::class, 'import'])->name('stages.import');
    Route::resource('stages', StageController::class);

    // Types de stages
    Route::resource('types-stages', TypeStageController::class);

    // Réservations
    Route::post('reservations/import', [AdminReservationController::class, 'import'])->name('reservations.import');
    Route::post('reservations/{reservation}', [AdminReservationController::class, 'update'])->name('reservations.update');
    Route::resource('reservations', AdminReservationController::class);

    // Tests psychotechniques
    Route::get('tests-psychos/archive', [TestPsychoController::class, 'archive'])->name('tests-psychos.archive');
    Route::post('tests-psychos/import', [TestPsychoController::class, 'import'])->name('tests-psychos.import');
    Route::resource('tests-psychos', TestPsychoController::class);

    // Types de tests psychotechniques
    Route::resource('types-tests-psychos', TypeTestPsychoController::class);

    // Réservations de tests psychotechniques
    Route::post('reservations-tests-psychos/import', [ReservationTestPsychoController::class, 'import'])->name('reservations-tests-psychos.import');
    Route::post('reservations-tests-psychos/{reservationsTestPsycho}', [ReservationTestPsychoController::class, 'update'])->name('reservations-tests-psychos.update');
    Route::resource('reservations-tests-psychos', ReservationTestPsychoController::class);

    // Clients
    Route::resource('clients', ClientController::class);

    // Contacts
    Route::get('contacts', [AdminContactController::class, 'index'])->name('contacts.index');
    Route::get('contacts/{contact}', [AdminContactController::class, 'show'])->name('contacts.show');
    Route::put('contacts/{contact}', [AdminContactController::class, 'update'])->name('contacts.update');
    Route::delete('contacts/{contact}', [AdminContactController::class, 'destroy'])->name('contacts.destroy');
    Route::patch('contacts/{contact}/toggle-read', [AdminContactController::class, 'toggleRead'])->name('contacts.toggle-read');

    // Configuration
    Route::get('configuration', [ConfigurationController::class, 'index'])->name('configuration.index');
    Route::post('configuration/payment-methods', [ConfigurationController::class, 'updatePaymentMethods'])->name('configuration.payment-methods');
    Route::post('configuration/general', [ConfigurationController::class, 'updateGeneral'])->name('configuration.general');
});

require __DIR__ . '/settings.php';
require __DIR__ . '/auth.php';

// Routes de test pour les emails (à supprimer en production)
if (app()->environment(['local', 'testing'])) {
    require __DIR__ . '/test-emails.php';
}
