# Data Table Component

This is a powerful data table component built using TanStack Table and Shadcn UI components. It provides features like sorting, filtering, column visibility, and pagination.

## Usage

Here's how to use the data table component in your admin pages:

```tsx
import { ColumnDef } from '@tanstack/react-table';
import { DataTable, DataTableColumnHeader } from '@/components/ui/data-table';

// Define your data type
interface YourDataType {
  id: number;
  name: string;
  // other properties...
}

// Define your columns
const columns: ColumnDef<YourDataType>[] = [
  {
    accessorKey: "name",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Name" />
    ),
    enableSorting: true,
    enableHiding: true,
  },
  // other columns...
];

// In your component
return (
  <DataTable
    title="Your Title"
    columns={columns}
    data={yourData}
    onAdd={handleAdd}
    onEdit={handleEdit}
    onDelete={handleDelete}
    pagination={{
      links: yourPagination.links,
      from: yourPagination.from,
      to: yourPagination.to,
      total: yourPagination.total
    }}
    searchableColumns={[
      {
        id: "name",
        title: "name"
      }
    ]}
    filterableColumns={[
      {
        id: "category",
        title: "category",
        options: categories.map(category => ({
          value: category.name,
          label: category.name
        }))
      }
    ]}
  />
);
```

## Props

The `DataTable` component accepts the following props:

- `title`: (optional) The title of the table
- `columns`: Column definitions using TanStack Table's `ColumnDef`
- `data`: Array of data items
- `onAdd`: (optional) Function to call when the add button is clicked
- `onEdit`: (optional) Function to call when the edit button is clicked for a row
- `onDelete`: (optional) Function to call when the delete button is clicked for a row
- `pagination`: (optional) Pagination information
  - `links`: Array of pagination links
  - `from`: Starting index of the current page
  - `to`: Ending index of the current page
  - `total`: Total number of items
- `searchableColumns`: (optional) Array of columns that can be searched
  - `id`: Column ID
  - `title`: Column title for display
- `filterableColumns`: (optional) Array of columns that can be filtered
  - `id`: Column ID
  - `title`: Column title for display
  - `options`: Array of filter options
    - `value`: Option value
    - `label`: Option label for display

## Column Definition

Each column should be defined using TanStack Table's `ColumnDef` type:

```tsx
{
  accessorKey: "propertyName", // The property name in your data
  header: ({ column }) => (
    <DataTableColumnHeader column={column} title="Column Title" />
  ),
  cell: ({ row }) => {
    // Custom cell rendering (optional)
    return <div>{row.getValue("propertyName")}</div>;
  },
  enableSorting: true, // Enable sorting for this column
  enableHiding: true, // Allow this column to be hidden
}
```

## Features

- **Sorting**: Click on column headers to sort
- **Filtering**: Use the filter inputs to filter data
- **Column Visibility**: Toggle column visibility using the "Columns" button
- **Pagination**: Navigate through pages of data
- **Search**: Search through searchable columns
- **Responsive**: Works on all screen sizes
