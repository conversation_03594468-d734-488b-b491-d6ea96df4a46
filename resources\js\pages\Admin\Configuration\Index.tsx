import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { useToast } from '@/hooks/use-toast';
import AppLayout from '@/layouts/app-layout';
import { Head, useForm } from '@inertiajs/react';
import { FormEventHandler, useEffect } from 'react';

interface PaymentMethods {
  paypal: boolean;
  sumup: boolean;
  virement: boolean;
  cheque: boolean;
  bon: boolean;
  paiement_sur_place: boolean;
}

interface ConfigurationProps {
  paymentMethods: PaymentMethods;
  configurations: any;
}

export default function ConfigurationIndex({ paymentMethods, configurations }: ConfigurationProps) {
  const { toast } = useToast();

  // Payment methods form
  const {
    data: paymentData,
    setData: setPaymentData,
    post: postPayment,
    processing: processingPayment,
    errors: paymentErrors,
    reset: resetPayment,
  } = useForm<PaymentMethods>({
    paypal: paymentMethods.paypal,
    sumup: paymentMethods.sumup,
    virement: paymentMethods.virement,
    cheque: paymentMethods.cheque,
    bon: paymentMethods.bon,
    paiement_sur_place: paymentMethods.paiement_sur_place,
  });

  // General configuration form
  const {
    data: generalData,
    setData: setGeneralData,
    post: postGeneral,
    processing: processingGeneral,
    errors: generalErrors,
    reset: resetGeneral,
  } = useForm({
    app_maintenance_mode: false,
    max_reservations_per_user: 10,
  });

  // Load general configuration values
  useEffect(() => {
    if (configurations?.general) {
      const generalConfigs = configurations.general;
      const maintenanceConfig = generalConfigs.find((c: any) => c.key === 'app_maintenance_mode');
      const maxReservationsConfig = generalConfigs.find((c: any) => c.key === 'max_reservations_per_user');

      setGeneralData({
        app_maintenance_mode: maintenanceConfig ? maintenanceConfig.value === '1' : false,
        max_reservations_per_user: maxReservationsConfig ? parseInt(maxReservationsConfig.value) : 10,
      });
    }
  }, [configurations]);

  const submitPaymentMethods: FormEventHandler = (e) => {
    e.preventDefault();

    postPayment(route('admin.configuration.payment-methods'), {
      onSuccess: () => {
        toast({
          title: 'Succès',
          description: 'Configuration des méthodes de paiement mise à jour avec succès.',
        });
      },
      onError: () => {
        toast({
          title: 'Erreur',
          description: 'Une erreur est survenue lors de la mise à jour.',
          variant: 'destructive',
        });
      },
    });
  };

  const submitGeneral: FormEventHandler = (e) => {
    e.preventDefault();

    postGeneral(route('admin.configuration.general'), {
      onSuccess: () => {
        toast({
          title: 'Succès',
          description: 'Configuration générale mise à jour avec succès.',
        });
      },
      onError: () => {
        toast({
          title: 'Erreur',
          description: 'Une erreur est survenue lors de la mise à jour.',
          variant: 'destructive',
        });
      },
    });
  };

  return (
    <AppLayout>
      <Head title="Configuration Générale" />

      <div className="p-6 space-y-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Configuration Générale</h1>
          <p className="text-muted-foreground">
            Gérez les paramètres généraux de l'application et les méthodes de paiement.
          </p>
        </div>

        <div className="grid gap-6">
          {/* Payment Methods Configuration */}
          <Card>
            <CardHeader>
              <CardTitle>Méthodes de Paiement</CardTitle>
              <CardDescription>
                Activez ou désactivez les méthodes de paiement disponibles pour les utilisateurs.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={submitPaymentMethods} className="space-y-6">
                <div className="grid gap-4">
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label htmlFor="paypal">PayPal</Label>
                      <p className="text-sm text-muted-foreground">
                        Paiement sécurisé via PayPal
                      </p>
                    </div>
                    <Switch
                      id="paypal"
                      checked={paymentData.paypal}
                      onCheckedChange={(checked) => setPaymentData('paypal', checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label htmlFor="sumup">SumUp</Label>
                      <p className="text-sm text-muted-foreground">
                        Paiement par carte bancaire via SumUp
                      </p>
                    </div>
                    <Switch
                      id="sumup"
                      checked={paymentData.sumup}
                      onCheckedChange={(checked) => setPaymentData('sumup', checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label htmlFor="virement">Virement bancaire</Label>
                      <p className="text-sm text-muted-foreground">
                        Paiement par virement bancaire
                      </p>
                    </div>
                    <Switch
                      id="virement"
                      checked={paymentData.virement}
                      onCheckedChange={(checked) => setPaymentData('virement', checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label htmlFor="cheque">Chèque</Label>
                      <p className="text-sm text-muted-foreground">
                        Paiement par chèque
                      </p>
                    </div>
                    <Switch
                      id="cheque"
                      checked={paymentData.cheque}
                      onCheckedChange={(checked) => setPaymentData('cheque', checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label htmlFor="bon">Bon de commande</Label>
                      <p className="text-sm text-muted-foreground">
                        Paiement par bon de commande
                      </p>
                    </div>
                    <Switch
                      id="bon"
                      checked={paymentData.bon}
                      onCheckedChange={(checked) => setPaymentData('bon', checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label htmlFor="paiement_sur_place">Paiement sur place</Label>
                      <p className="text-sm text-muted-foreground">
                        Paiement directement sur le lieu
                      </p>
                    </div>
                    <Switch
                      id="paiement_sur_place"
                      checked={paymentData.paiement_sur_place}
                      onCheckedChange={(checked) => setPaymentData('paiement_sur_place', checked)}
                    />
                  </div>
                </div>

                <Button type="submit" disabled={processingPayment}>
                  {processingPayment ? 'Mise à jour...' : 'Sauvegarder les méthodes de paiement'}
                </Button>
              </form>
            </CardContent>
          </Card>

          {/* General Configuration */}
          <Card>
            <CardHeader>
              <CardTitle>Configuration Générale</CardTitle>
              <CardDescription>
                Paramètres généraux de l'application.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={submitGeneral} className="space-y-6">
                <div className="grid gap-4">
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label htmlFor="maintenance">Mode maintenance</Label>
                      <p className="text-sm text-muted-foreground">
                        Activer le mode maintenance de l'application
                      </p>
                    </div>
                    <Switch
                      id="maintenance"
                      checked={generalData.app_maintenance_mode}
                      onCheckedChange={(checked) => setGeneralData('app_maintenance_mode', checked)}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="max_reservations">Nombre maximum de réservations par utilisateur</Label>
                    <Input
                      id="max_reservations"
                      type="number"
                      min="1"
                      max="100"
                      value={generalData.max_reservations_per_user}
                      onChange={(e) => setGeneralData('max_reservations_per_user', parseInt(e.target.value))}
                      className="w-32"
                    />
                    <p className="text-sm text-muted-foreground">
                      Limite le nombre de réservations qu'un utilisateur peut effectuer
                    </p>
                  </div>
                </div>

                <Button type="submit" disabled={processingGeneral}>
                  {processingGeneral ? 'Mise à jour...' : 'Sauvegarder la configuration générale'}
                </Button>
              </form>
            </CardContent>
          </Card>
        </div>
      </div>
    </AppLayout>
  );
}
