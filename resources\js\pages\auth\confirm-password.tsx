// Components
import { useForm } from '@inertiajs/react';
import { LoaderCircle } from 'lucide-react';
import { FormEventHandler } from 'react';

import InputError from '@/components/input-error';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import AuthLayout from '@/layouts/auth-layout';

export default function ConfirmPassword() {
  const { data, setData, post, processing, errors, reset } = useForm<Required<{ password: string }>>({
    password: '',
  });

  const submit: FormEventHandler = (e) => {
    e.preventDefault();

    post(route('password.confirm'), {
      onFinish: () => reset('password'),
    });
  };

  return (
    <AuthLayout
      title="Confirmer votre mot de passe"
      description="Ceci est une zone sécurisée de l'application. Veuillez confirmer votre mot de passe avant de continuer."
    >
      <Card className="rounded-xl border shadow-sm">
        <CardContent className="px-6 py-8">
          <form onSubmit={submit}>
            <div className="space-y-6">
              <div className="grid gap-2">
                <Label htmlFor="password">Mot de passe</Label>
                <Input
                  id="password"
                  type="password"
                  name="password"
                  placeholder="Mot de passe"
                  autoComplete="current-password"
                  value={data.password}
                  autoFocus
                  onChange={(e) => setData('password', e.target.value)}
                />

                <InputError message={errors.password} />
              </div>

              <div className="flex items-center">
                <Button className="w-full" disabled={processing}>
                  {processing && <LoaderCircle className="h-4 w-4 animate-spin" />}
                  Confirmer le mot de passe
                </Button>
              </div>
            </div>
          </form>
        </CardContent>
      </Card>
    </AuthLayout>
  );
}
