import { Head, Link, usePage } from '@inertiajs/react';
import { Button } from '@/components/ui/button';
import { useFlashMessages } from '@/hooks/use-flash-messages';
import { type SharedData } from '@/types';
import { ReactNode, useEffect, useState } from 'react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import { ChevronDown, User, LogOut, Settings, UserCircle, Calendar, Package, Menu } from 'lucide-react';
import AppearanceToggle from '@/components/appearance-toggle';
import { DropdownMenuSeparator } from '@/components/ui/dropdown-menu';
import { cn } from '@/lib/utils';
import { Sheet, SheetContent, SheetHeader, SheetTitle, SheetTrigger } from '@/components/ui/sheet';
import { useIsMobile } from '@/hooks/use-mobile';

interface FrontLayoutProps {
  children: ReactNode;
  title?: string;
}

export default function FrontLayout({ children, title = 'Accueil' }: FrontLayoutProps) {
  const { auth } = usePage<SharedData>().props;
  const [isScrolled, setIsScrolled] = useState(false);
  const isMobile = useIsMobile();
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  // Handle scroll event to detect when page is scrolled
  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Utiliser le hook pour afficher les messages flash
  useFlashMessages();

  // Navigation items for reuse in both desktop and mobile
  const renderNavItems = () => (
    <>
      <Link href={route('home')} className="font-medium hover:bg-primary hover:text-white px-4 py-2 rounded-none transition-colors">
        ACCUEIL
      </Link>

      <DropdownMenu>
        <DropdownMenuTrigger className="flex items-center gap-1 font-medium hover:bg-primary hover:text-white px-3 py-2 rounded-none transition-colors group">
          PERMIS A POINTS <ChevronDown className="h-4 w-4 group-hover:text-white" />
        </DropdownMenuTrigger>
        <DropdownMenuContent className="bg-primary/90 backdrop-blur-sm border-primary text-white p-1 rounded-none">
          <DropdownMenuItem asChild className="hover:bg-primary/80 rounded-none px-4 py-2">
            <Link href={route('permis-points')} className="w-full text-white">Permis à points</Link>
          </DropdownMenuItem>
          <DropdownMenuItem asChild className="hover:bg-primary/80 rounded-none px-4 py-2">
            <Link href={route('sensibilisation-accidents')} className="w-full text-white">Programme du stage</Link>
          </DropdownMenuItem>
          <DropdownMenuItem asChild className="hover:bg-primary/80 rounded-none px-4 py-2">
            <Link href={route('stage-france-permis')} className="w-full text-white">Prix et inscription</Link>
          </DropdownMenuItem>
          <DropdownMenuItem asChild className="hover:bg-primary/80 rounded-none px-4 py-2">
            <Link href={route('conditions-generales')} className="w-full text-white">Conditions générales</Link>
          </DropdownMenuItem>
          <DropdownMenuItem asChild className="hover:bg-primary/80 rounded-none px-4 py-2">
            <Link href={route('permis-en-danger')} className="w-full text-white">Permis en danger</Link>
          </DropdownMenuItem>
          <DropdownMenuItem asChild className="hover:bg-primary/80 rounded-none px-4 py-2">
            <Link href={route('antai')} className="w-full text-white">ANTAI</Link>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      <div className="flex flex-col items-center hover:bg-primary hover:text-white px-3 py-2">
        <Link href={route('stages')} className="font-medium">LES STAGES</Link>
        <span className="text-xs">Permis à points</span>
      </div>

      <Link href={route('tests-psychotechniques')} className="font-medium hover:bg-primary hover:text-white px-3 py-2">TESTS PSYCHOTECHNIQUES</Link>

      <div className="flex flex-col items-center hover:bg-primary hover:text-white px-4 py-2">
        <Link href={route('pssm')} className="font-medium">STAGES PSSM</Link>
        <span className="text-xs">Premiers secours santé mentale</span>
      </div>

      <Link href={route('dossiers')} className="font-medium hover:bg-primary hover:text-white px-3 py-2">DOSSIERS</Link>
      <Link href={route('contact')} className="font-medium hover:bg-primary hover:text-white px-3 py-2">CONTACT</Link>
    </>
  );

  // Mobile navigation menu items
  const renderMobileNavItems = () => (
    <div className="flex flex-col space-y-2 py-4">
      <Link
        href={route('home')}
        className="font-medium px-4 py-3 hover:bg-primary hover:text-white transition-colors"
        onClick={() => setIsMenuOpen(false)}
      >
        ACCUEIL
      </Link>

      <div className="border-t border-gray-200 dark:border-gray-800 my-2"></div>

      <div className="px-4 py-2 font-medium">PERMIS A POINTS</div>
      <Link
        href={route('permis-points')}
        className="px-6 py-2 hover:bg-primary/10 transition-colors"
        onClick={() => setIsMenuOpen(false)}
      >
        Permis à points
      </Link>
      <Link
        href={route('sensibilisation-accidents')}
        className="px-6 py-2 hover:bg-primary/10 transition-colors"
        onClick={() => setIsMenuOpen(false)}
      >
        Programme du stage
      </Link>
      <Link
        href={route('stage-france-permis')}
        className="px-6 py-2 hover:bg-primary/10 transition-colors"
        onClick={() => setIsMenuOpen(false)}
      >
        Prix et inscription
      </Link>
      <Link
        href={route('conditions-generales')}
        className="px-6 py-2 hover:bg-primary/10 transition-colors"
        onClick={() => setIsMenuOpen(false)}
      >
        Conditions générales
      </Link>
      <Link
        href={route('permis-en-danger')}
        className="px-6 py-2 hover:bg-primary/10 transition-colors"
        onClick={() => setIsMenuOpen(false)}
      >
        Permis en danger
      </Link>
      <Link
        href={route('antai')}
        className="px-6 py-2 hover:bg-primary/10 transition-colors"
        onClick={() => setIsMenuOpen(false)}
      >
        ANTAI
      </Link>

      <div className="border-t border-gray-200 dark:border-gray-800 my-2"></div>

      <Link
        href={route('stages')}
        className="font-medium px-4 py-3 hover:bg-primary hover:text-white transition-colors"
        onClick={() => setIsMenuOpen(false)}
      >
        LES STAGES <span className="text-xs block">Permis à points</span>
      </Link>

      <Link
        href={route('tests-psychotechniques')}
        className="font-medium px-4 py-3 hover:bg-primary hover:text-white transition-colors"
        onClick={() => setIsMenuOpen(false)}
      >
        TESTS PSYCHOTECHNIQUES
      </Link>

      <Link
        href={route('pssm')}
        className="font-medium px-4 py-3 hover:bg-primary hover:text-white transition-colors"
        onClick={() => setIsMenuOpen(false)}
      >
        STAGES PSSM <span className="text-xs block">Premiers secours santé mentale</span>
      </Link>

      <Link
        href={route('dossiers')}
        className="font-medium px-4 py-3 hover:bg-primary hover:text-white transition-colors"
        onClick={() => setIsMenuOpen(false)}
      >
        DOSSIERS
      </Link>

      <Link
        href={route('contact')}
        className="font-medium px-4 py-3 hover:bg-primary hover:text-white transition-colors"
        onClick={() => setIsMenuOpen(false)}
      >
        CONTACT
      </Link>

      <div className="border-t border-gray-200 dark:border-gray-800 my-2"></div>

      {auth.user ? (
        <>
          <div className="px-4 py-2 font-medium">
            {auth.user.prenom} {auth.user.nom}
          </div>

          {auth.user.role === 'admin' && (
            <Link
              href={route('admin.dashboard')}
              className="px-4 py-2 flex items-center hover:bg-primary/10 transition-colors"
              onClick={() => setIsMenuOpen(false)}
            >
              <Settings className="mr-2 h-4 w-4" />
              Dashboard Admin
            </Link>
          )}

          {auth.user.role === 'client' && (
            <>
              <Link
                href={route('client.dashboard')}
                className="px-4 py-2 flex items-center hover:bg-primary/10 transition-colors"
                onClick={() => setIsMenuOpen(false)}
              >
                <UserCircle className="mr-2 h-4 w-4" />
                Mon compte
              </Link>
              <Link
                href={route('client.reservations')}
                className="px-4 py-2 flex items-center hover:bg-primary/10 transition-colors"
                onClick={() => setIsMenuOpen(false)}
              >
                <Package className="mr-2 h-4 w-4" />
                Mes réservations
              </Link>
              <Link
                href={route('client.stages')}
                className="px-4 py-2 flex items-center hover:bg-primary/10 transition-colors"
                onClick={() => setIsMenuOpen(false)}
              >
                <Calendar className="mr-2 h-4 w-4" />
                Mes stages
              </Link>
            </>
          )}

          <Link
            href={route('logout')}
            method="post"
            className="px-4 py-2 flex items-center text-destructive hover:bg-destructive/10 transition-colors"
            onClick={() => setIsMenuOpen(false)}
          >
            <LogOut className="mr-2 h-4 w-4" />
            Se déconnecter
          </Link>
        </>
      ) : (
        <>
          <Link
            href={route('login')}
            className="px-4 py-2 hover:bg-primary/10 transition-colors"
            onClick={() => setIsMenuOpen(false)}
          >
            Se connecter
          </Link>
          <Link
            href={route('register')}
            className="px-4 py-2 hover:bg-primary/10 transition-colors"
            onClick={() => setIsMenuOpen(false)}
          >
            S'inscrire
          </Link>
        </>
      )}
    </div>
  );

  return (
    <>
      <Head title={title} />
      <div className="flex min-h-screen flex-col bg-background text-foreground">
        {/* Navbar - Fixed on scroll */}
        <header className={cn(
          "sticky top-0 w-full z-50 transition-all duration-200",
          isScrolled
            ? "border-b bg-white/90 dark:bg-black/90 backdrop-blur-sm shadow-sm"
            : "border-b bg-white dark:bg-black"
        )}>
          <div className="container mx-auto flex items-center justify-center px-4 py-0">
            <div className="flex items-center gap-4">


              <img src="/logo.jpg" alt="Logo" className="h-10" />

              {/* Desktop Navigation */}
              <nav className="hidden gap-1 md:flex items-center">
                {renderNavItems()}
              </nav>
            </div>

            {/* User menu and appearance toggle */}
            <div className="flex items-center gap-2">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="icon" className="h-9 w-9 rounded-md">
                    <User className="h-5 w-5" />
                    <span className="sr-only">Menu utilisateur</span>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  {auth.user ? (
                    <>
                      {/* Afficher le nom de l'utilisateur */}
                      <div className="px-2 py-1.5 text-sm font-medium">
                        {auth.user.prenom} {auth.user.nom}
                      </div>
                      <DropdownMenuSeparator />

                      {/* Menu pour les administrateurs */}
                      {auth.user.role === 'admin' && (
                        <>
                          <DropdownMenuItem asChild>
                            <Link href={route('admin.dashboard')} className="w-full">
                              <Settings className="mr-2 h-4 w-4" />
                              Dashboard Admin
                            </Link>
                          </DropdownMenuItem>
                        </>
                      )}

                      {/* Menu pour les clients */}
                      {auth.user.role === 'client' && (
                        <>
                          <DropdownMenuItem asChild>
                            <Link href={route('client.dashboard')} className="w-full">
                              <UserCircle className="mr-2 h-4 w-4" />
                              Mon compte
                            </Link>
                          </DropdownMenuItem>
                          <DropdownMenuItem asChild>
                            <Link href={route('client.reservations')} className="w-full">
                              <Package className="mr-2 h-4 w-4" />
                              Mes réservations
                            </Link>
                          </DropdownMenuItem>
                          <DropdownMenuItem asChild>
                            <Link href={route('client.stages')} className="w-full">
                              <Calendar className="mr-2 h-4 w-4" />
                              Mes stages
                            </Link>
                          </DropdownMenuItem>
                        </>
                      )}

                      <DropdownMenuSeparator />

                      {/* Déconnexion pour tous les utilisateurs */}
                      <DropdownMenuItem asChild>
                        <Link href={route('logout')} method="post" className="w-full text-destructive">
                          <LogOut className="mr-2 h-4 w-4" />
                          Se déconnecter
                        </Link>
                      </DropdownMenuItem>
                    </>
                  ) : (
                    <>
                      <DropdownMenuItem asChild>
                        <Link href={route('login')} className="w-full">
                          Se connecter
                        </Link>
                      </DropdownMenuItem>
                      <DropdownMenuItem asChild>
                        <Link href={route('register')} className="w-full">
                          S'inscrire
                        </Link>
                      </DropdownMenuItem>
                    </>
                  )}
                </DropdownMenuContent>
              </DropdownMenu>
              <AppearanceToggle className="ml-2" />
            </div>
            {/* Mobile menu hamburger */}
              <div className="md:hidden">
                <Sheet open={isMenuOpen} onOpenChange={setIsMenuOpen}>
                  <SheetTrigger asChild>
                    <Button variant="ghost" size="icon" className="h-9 w-9">
                      <Menu className="h-5 w-5" />
                      <span className="sr-only">Menu</span>
                    </Button>
                  </SheetTrigger>
                  <SheetContent side="left" className="w-[280px] sm:w-[350px] overflow-y-auto">
                    <SheetHeader>
                      <SheetTitle className="flex items-center justify-start">
                        <img src="/logo.jpg" alt="Logo" className="h-10" />
                      </SheetTitle>
                    </SheetHeader>
                    {renderMobileNavItems()}
                  </SheetContent>
                </Sheet>
              </div>
          </div>
        </header>

        {/* Main Content */}
        <main className="flex-1">
          {children}
        </main>

        {/* Footer */}
        <footer className="relative bg-[#111] text-white">
          {/* Background Image */}
          <div
            className="absolute inset-0 z-0"
            style={{
              background: '#111 url(/images/buildings.png) repeat-x left bottom',
              opacity: 1
            }}
          ></div>
          <div className="container relative z-10 mx-auto px-4">
            {/* Top Footer */}
            <div className="grid grid-cols-1 gap-8 py-12 md:grid-cols-4">
              <div>
                <h4 className="mb-6 text-lg font-bold text-primary">À PROPOS DE NOUS</h4>
                <p className="mb-6 text-sm text-gray-300">
                  Striatum propose des stages de récupération de points de permis pas cher sur Toulon et toute la France.
                </p>
                <div className="flex gap-4">
                  <a href="#" aria-label="Facebook" className="flex h-10 w-10 items-center justify-center rounded-full bg-primary/20 text-primary hover:bg-primary hover:text-white transition-colors">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z"></path></svg>
                  </a>
                  <a href="#" aria-label="Twitter" className="flex h-10 w-10 items-center justify-center rounded-full bg-primary/20 text-primary hover:bg-primary hover:text-white transition-colors">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z"></path></svg>
                  </a>
                  <a href="#" aria-label="Google" className="flex h-10 w-10 items-center justify-center rounded-full bg-primary/20 text-primary hover:bg-primary hover:text-white transition-colors">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10z"></path><path d="m2 12 3.76-3.76a9 9 0 0 1 14.48 0"></path><path d="M2 12h4"></path><path d="M22 12h-4"></path></svg>
                  </a>
                </div>
              </div>

              <div>
                <h4 className="mb-6 text-lg font-bold text-primary">NOS STAGES</h4>
                <ul className="space-y-3 text-sm text-gray-300">
                  <li>
                    <Link href={route('stages')} className="hover:text-primary transition-colors flex items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2 text-primary" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><polyline points="9 18 15 12 9 6"></polyline></svg>
                      Stages de récupération de points
                    </Link>
                  </li>
                  <li>
                    <Link href={route('sensibilisation-accidents')} className="hover:text-primary transition-colors flex items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2 text-primary" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><polyline points="9 18 15 12 9 6"></polyline></svg>
                      Programme de stage de sensibilisation
                    </Link>
                  </li>
                  <li>
                    <Link href={route('stage-france-permis')} className="hover:text-primary transition-colors flex items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2 text-primary" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><polyline points="9 18 15 12 9 6"></polyline></svg>
                      Prix et inscription
                    </Link>
                  </li>
                  <li>
                    <Link href={route('tests-psychotechniques')} className="hover:text-primary transition-colors flex items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2 text-primary" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><polyline points="9 18 15 12 9 6"></polyline></svg>
                      Tests psychotechniques
                    </Link>
                  </li>
                </ul>
              </div>

              <div>
                <h4 className="mb-6 text-lg font-bold text-primary">LIENS UTILES</h4>
                <ul className="space-y-3 text-sm text-gray-300">
                  <li>
                    <Link href={route('permis-points')} className="hover:text-primary transition-colors flex items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2 text-primary" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><polyline points="9 18 15 12 9 6"></polyline></svg>
                      Permis à points
                    </Link>
                  </li>
                  <li>
                    <Link href={route('permis-en-danger')} className="hover:text-primary transition-colors flex items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2 text-primary" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><polyline points="9 18 15 12 9 6"></polyline></svg>
                      Permis en danger
                    </Link>
                  </li>
                  <li>
                    <Link href={route('conditions-generales')} className="hover:text-primary transition-colors flex items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2 text-primary" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><polyline points="9 18 15 12 9 6"></polyline></svg>
                      Conditions générales
                    </Link>
                  </li>
                  <li>
                    <Link href={route('contact')} className="hover:text-primary transition-colors flex items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2 text-primary" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><polyline points="9 18 15 12 9 6"></polyline></svg>
                      Contact
                    </Link>
                  </li>
                </ul>
              </div>

              <div>
                <h4 className="mb-6 text-lg font-bold text-primary">CONTACTEZ-NOUS</h4>
                <ul className="space-y-3 text-sm text-gray-300">
                  <li className="flex items-start">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-3 text-primary flex-shrink-0 mt-0.5" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z"></path><circle cx="12" cy="10" r="3"></circle></svg>
                    <span>Palais Vauban 12 av Jean Moulin 83000 TOULON</span>
                  </li>
                  <li className="flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-3 text-primary flex-shrink-0" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><rect width="20" height="16" x="2" y="4" rx="2"></rect><path d="m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7"></path></svg>
                    <span><EMAIL></span>
                  </li>
                  <li className="flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-3 text-primary flex-shrink-0" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"></path></svg>
                    <span>06 58 77 23 85</span>
                  </li>
                  {/* <li className="flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-3 text-primary flex-shrink-0" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10"></path></svg>
                    <span>Service d'information permis de conduire</span>
                  </li> */}
                </ul>
              </div>
            </div>
          </div>

          {/* Bottom Footer */}
          <div className="relative  border-gray-900 py-6 text-center text-sm text-gray-400">
            <div className="container relative z-10 mx-auto px-4">
              <div className="flex flex-col md:flex-row justify-between items-center">
                <div>
                  © 2012 - {new Date().getFullYear()} Striatum Group. Tous droits réservés.
                </div>
                <div className="mt-4 md:mt-0">
                  <img src="/images/carte.png" alt="Méthodes de paiement" className="h-6" />
                </div>
              </div>
            </div>
          </div>
        </footer>
      </div>
    </>
  );
}



