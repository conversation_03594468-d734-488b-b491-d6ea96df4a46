import { Departement } from '@/types';
import * as am5 from '@amcharts/amcharts5';
import am5geodata_franceDepartments2Low from '@amcharts/amcharts5-geodata/franceDepartments2Low';
import * as am5map from '@amcharts/amcharts5/map';
import am5themes_Animated from '@amcharts/amcharts5/themes/Animated';
import { router } from '@inertiajs/react';
import { useLayoutEffect, useRef } from 'react';

interface FranceMapProps {
    departements: Departement[];
}

function FranceMap({ departements = [] }: FranceMapProps) {
    // Use a ref for the chart container div
    const chartDivRef = useRef<HTMLDivElement>(null);
    // Use a ref to store the root element for cleanup
    const rootRef = useRef<am5.Root | null>(null);
    // Use a ref to store the polygon series for updates
    const polygonSeriesRef = useRef<am5map.MapPolygonSeries | null>(null);

    // Main chart creation effect
    useLayoutEffect(() => {
        // --- Create root element ---
        // https://www.amcharts.com/docs/v5/getting-started/#Root_element
        if (!chartDivRef.current) return;

        const root = am5.Root.new(chartDivRef.current);
        rootRef.current = root; // Store root for cleanup

        // --- Set themes ---
        // https://www.amcharts.com/docs/v5/concepts/themes/
        root.setThemes([am5themes_Animated.new(root)]);

        // --- Create map chart ---
        // https://www.amcharts.com/docs/v5/charts/map-chart/
        const chart = root.container.children.push(
            am5map.MapChart.new(root, {
                panX: 'translateX', // Enable panning
                panY: 'translateY',
                projection: am5map.geoMercator(), // Choose a projection
                // homeGeoPoint: { latitude: 46.2, longitude: 2.2 }, // Center France
                // homeZoomLevel: 5 // Initial zoom level
            }),
        );

        // Define colors for states
        const primaryColor = am5.color(0xd46893); // Primary color for departments with stages
        const inactiveColor = am5.color(0xcccccc); // Grey for departments without stages
        const hoverColor = am5.color(0x88bbea); // Hover color

        // --- Create map polygon series (departments) ---
        // https://www.amcharts.com/docs/v5/charts/map-chart/map-polygon-series/
        const polygonSeries = chart.series.push(
            am5map.MapPolygonSeries.new(root, {
                geoJSON: am5geodata_franceDepartments2Low, // Use the imported GeoJSON
                valueField: 'value', // Optional: if you have data values
                calculateAggregates: true, // Optional: useful for heatmaps etc.
            }),
        );

        // Store the polygon series in a ref for later updates
        polygonSeriesRef.current = polygonSeries;

        // Create a map of department codes for quick lookup
        const departementCodes = new Set(departements.map((dep) => dep.code));

        // --- Configure polygon appearance and interaction ---
        polygonSeries.mapPolygons.template.setAll({
            tooltipText: '{name}', // Show department name on hover
            // Default state (will be overridden for departments with stages)
            stroke: am5.color(0xffffff), // White borders
            strokeWidth: 0.5,
        });
        
        // --- Ajoutez l'adaptateur critique ---
        polygonSeries.mapPolygons.template.adapters.add('fill', (fill, target) => {
            const dataContext = target.dataItem?.dataContext as { id: string };
            return dataContext?.id && departementCodes.has(dataContext.id) ? primaryColor : inactiveColor;
        });

        // --- Define the hover state ---
        polygonSeries.mapPolygons.template.states.create('hover', {
            fill: hoverColor, // Lighter color on hover
        });

        // --- Add click event listener ---
        polygonSeries.mapPolygons.template.events.on('click', function (ev) {
            const dataItem = ev.target.dataItem;
            if (!dataItem) return;

            // Access the ID from the dataContext property which contains the GeoJSON properties
            const dataContext = dataItem.dataContext as { id: string };
            if (!dataContext || !dataContext.id) return;

            const depCode = dataContext.id;

            // Only navigate if this department has stages
            if (departementCodes.has(depCode)) {
                // Navigate to the stages page with the department code as a parameter
                router.get(route('stages'), {
                    departement: depCode,
                    mois: 'all',
                });
            } else {
                // Optionally, you can show a message or do nothing when clicking on a department without stages
                console.log('This department has no stages.');
                return;
            }
        });

        // --- Add zoom control ---
        // https://www.amcharts.com/docs/v5/charts/map-chart/map-zoom-control/
        chart.set('zoomControl', am5map.ZoomControl.new(root, {}));

        // --- Cleanup on component unmount ---
        return () => {
            if (rootRef.current) {
                rootRef.current.dispose();
            }
        };
    }, [departements]); // Include departements in the dependency array

    // Style the container div
    const mapStyle = {
        width: '100%',
        height: '600px', // Adjust height as needed
    };

    return <div ref={chartDivRef} style={mapStyle}></div>;
}

export default FranceMap;
