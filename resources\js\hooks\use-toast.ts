import { toast as sonnerToast } from "sonner";

interface ToastOptions {
  title: string;
  description?: string;
  variant?: 'default' | 'destructive' | 'success' | 'warning' | 'info';
}

export function useToast() {
  const toast = (options: ToastOptions | string) => {
    if (typeof options === 'string') {
      sonnerToast(options);
      return;
    }

    const { title, description, variant = 'default' } = options;
    const message = description ? `${title}: ${description}` : title;

    switch (variant) {
      case 'destructive':
        sonnerToast.error(message);
        break;
      case 'success':
        sonnerToast.success(message);
        break;
      case 'warning':
        sonnerToast.warning(message);
        break;
      case 'info':
        sonnerToast.info(message);
        break;
      default:
        sonnerToast(message);
        break;
    }
  };

  return {
    toast,
    success: (message: string) => {
      sonnerToast.success(message);
    },
    error: (message: string) => {
      sonnerToast.error(message);
    },
    warning: (message: string) => {
      sonnerToast.warning(message);
    },
    info: (message: string) => {
      sonnerToast.info(message);
    },
  };
}
