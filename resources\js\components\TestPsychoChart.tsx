import * as React from "react"
import { Area, AreaChart, CartesianGrid, XAxis, Tooltip, ResponsiveContainer } from "recharts"
import { TrendingUp, TrendingDown } from "lucide-react"

import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"

interface TestPsychoChartProps {
  data: {
    month: string;
    tests: number;
  }[];
  trend: {
    percentage: number;
    isUp: boolean;
  };
}

export function TestPsychoChart({ data, trend }: TestPsychoChartProps) {
  const [timeRange, setTimeRange] = React.useState("6m")

  // Vérifier si les données sont valides
  const hasValidData = Array.isArray(data) && data.length > 0;

  console.log("Test Psycho Chart Data:", data);
  console.log("Test Psycho Chart Trend:", trend);

  // Filtrer les données en fonction de la plage de temps sélectionnée
  const filteredData = React.useMemo(() => {
    if (!hasValidData) return [];

    // Pour cet exemple, nous utilisons toutes les données car nous n'avons que 6 mois
    // Dans un cas réel, vous pourriez filtrer en fonction de timeRange
    return data;
  }, [data, timeRange, hasValidData]);

  return (
    <Card>
      <CardHeader className="flex items-center gap-2 space-y-0 border-b py-5 sm:flex-row">
        <div className="grid flex-1 gap-1 text-center sm:text-left">
          <CardTitle>Tests Psychotechniques</CardTitle>
          <CardDescription>
            Évolution des réservations de tests psychotechniques
          </CardDescription>
        </div>
        <Select value={timeRange} onValueChange={setTimeRange}>
          <SelectTrigger
            className="w-[160px] rounded-lg sm:ml-auto"
            aria-label="Sélectionner une période"
          >
            <SelectValue placeholder="6 derniers mois" />
          </SelectTrigger>
          <SelectContent className="rounded-xl">
            <SelectItem value="6m" className="rounded-lg">
              6 derniers mois
            </SelectItem>
            <SelectItem value="3m" className="rounded-lg">
              3 derniers mois
            </SelectItem>
            <SelectItem value="1m" className="rounded-lg">
              Dernier mois
            </SelectItem>
          </SelectContent>
        </Select>
      </CardHeader>
      <CardContent className="px-2 pt-4 sm:px-6 sm:pt-6">
        {!hasValidData ? (
          <div className="flex h-[250px] items-center justify-center text-muted-foreground">
            Aucune donnée disponible pour le graphique
          </div>
        ) : (
          <div className="h-[250px] w-full">
            <ResponsiveContainer width="100%" height="100%">
              <AreaChart data={filteredData}>
                <defs>
                  <linearGradient id="fillTests" x1="0" y1="0" x2="0" y2="1">
                    <stop
                      offset="5%"
                      stopColor="var(--chart-2)"
                      stopOpacity={0.8}
                    />
                    <stop
                      offset="95%"
                      stopColor="var(--chart-2)"
                      stopOpacity={0.1}
                    />
                  </linearGradient>
                </defs>
                <CartesianGrid vertical={false} strokeDasharray="3 3" />
                <XAxis
                  dataKey="month"
                  tickLine={false}
                  axisLine={false}
                  tickMargin={8}
                  minTickGap={32}
                  tickFormatter={(value) => value.slice(0, 3)}
                />
                <Tooltip
                  contentStyle={{
                    backgroundColor: 'var(--background)',
                    border: '1px solid var(--border)',
                    borderRadius: '0.5rem',
                    padding: '0.5rem'
                  }}
                  formatter={(value) => [`${value} réservations`, 'Tests Psycho']}
                  labelFormatter={(value) => `${value}`}
                />
                <Area
                  name="Tests Psycho"
                  dataKey="tests"
                  type="monotone"
                  fill="url(#fillTests)"
                  stroke="var(--chart-2)"
                  strokeWidth={2}
                />
              </AreaChart>
            </ResponsiveContainer>
          </div>
        )}
      </CardContent>
      <CardFooter className="flex-col items-start gap-2 text-sm">
        <div className="flex gap-2 font-medium leading-none">
          {trend.isUp ? (
            <>
              En hausse de {trend.percentage}% ce mois-ci <TrendingUp className="h-4 w-4 text-green-500" />
            </>
          ) : (
            <>
              En baisse de {trend.percentage}% ce mois-ci <TrendingDown className="h-4 w-4 text-red-500" />
            </>
          )}
        </div>
        <div className="leading-none text-muted-foreground">
          Évolution des réservations de tests psychotechniques pour les 6 derniers mois
        </div>
      </CardFooter>
    </Card>
  )
}
