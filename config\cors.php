<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Cross-Origin Resource Sharing (CORS) Configuration
    |--------------------------------------------------------------------------
    |
    | Here you may configure your settings for cross-origin resource sharing
    | or "CORS". This determines what cross-origin operations may execute
    | in web browsers. You are free to adjust these settings as needed.
    |
    | To learn more: https://developer.mozilla.org/en-US/docs/Web/HTTP/CORS
    |
    */

    'paths' => ['api/*', 'sanctum/csrf-cookie', 'paiement/*', 'paypal/*', 'sumup/*'],

    'allowed_methods' => ['*'],

    'allowed_origins' => ['*', 'https://www.sandbox.paypal.com', 'https://www.paypal.com', 'https://api.sumup.com'],

    'allowed_origins_patterns' => ['/^https:\/\/.*\.paypal\.com$/', '/^https:\/\/.*\.sumup\.com$/'],

    'allowed_headers' => ['*'],

    'exposed_headers' => [],

    'max_age' => 0,

    'supports_credentials' => true,

];
