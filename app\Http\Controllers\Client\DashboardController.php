<?php

namespace App\Http\Controllers\Client;

use App\Http\Controllers\Controller;
use App\Models\Reservation;
use App\Models\ReservationTestPsycho;

use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;

class DashboardController extends Controller
{
    public function index()
    {
        $user = Auth::user();

        // Récupérer les réservations de stages
        $reservations = Reservation::with(['stage.lieu.ville.departement', 'typeStage'])
            ->where('user_id', $user->id)
            ->get();

        // Récupérer les réservations de stages
        $reservationsTest = ReservationTestPsycho::with(['testPsycho.lieu.ville.departement', 'typeTestPsycho'])
            ->where('user_id', $user->id)
            ->get();

        // Réservations en cours (statut confirmée et date du stage dans le futur)
        $reservationsEnCours = $reservations->filter(function ($reservation) {
            return $reservation->statut === 'confirmée' &&
                   $reservation->stage &&
                   $reservation->stage->date_debut > now();
        })->count();

        // Réservations en cours (statut confirmée et date du stage dans le futur)
        $reservationsTestEnCours = $reservationsTest->filter(function ($reservation) {
            return $reservation->statut === 'confirmée' &&
                   $reservation->testPsycho &&
                   $reservation->testPsycho->date > now();
        })->count();

        // Stages à venir
        $stagesAVenir = $reservations->filter(function ($reservation) {
            return $reservation->stage &&
                   $reservation->stage->date_debut > now();
        })->count();

        // Récupérer les réservations de tests psychotechniques
        $testsCount = 0;
        $testsConfirmes = 0;
        $testsEnAttente = 0;
        $testsAnnules = 0;
        $testsAVenir = 0;
        $reservationsTests = [];

        if (class_exists('App\\Models\\ReservationTestPsycho')) {
            $reservationsTests = ReservationTestPsycho::with(['testPsycho.lieu.ville.departement', 'typeTestPsycho'])
                ->where('user_id', $user->id)
                ->get();

            $testsCount = $reservationsTests->count();

            // Compter les tests par statut
            $testsConfirmes = $reservationsTests->where('statut', 'confirmée')->count();
            $testsEnAttente = $reservationsTests->where('statut', 'en attente')->count();
            $testsAnnules = $reservationsTests->where('statut', 'annulée')->count();

            // Tests à venir (date du test dans le futur)
            $testsAVenir = $reservationsTests->filter(function ($reservation) {
                return $reservation->testPsycho &&
                       $reservation->testPsycho->date > now();
            })->count();
        }

        // Calculer le montant total des paiements
        $totalPaiements = $reservations->filter(function ($reservation) {
            return $reservation->statut === 'confirmée' && $reservation->date_paiement;
        })->sum(function ($reservation) {
            return $reservation->stage ? $reservation->stage->prix : 0;
        });

        // Calculer le montant total des paiements pour les tests psychotechniques
        $totalPaiementsTests = 0;
        if (!empty($reservationsTests)) {
            $totalPaiementsTests = $reservationsTests->filter(function ($reservation) {
                return $reservation->statut === 'confirmée' && $reservation->date_paiement;
            })->sum(function ($reservation) {
                return $reservation->testPsycho ? $reservation->testPsycho->prix : 0;
            });
        }

        // Montant total des paiements (stages + tests)
        $totalPaiements += $totalPaiementsTests;

        // Récupérer les activités récentes (5 dernières réservations de stages)
        $activitesRecentesStages = $reservations->sortByDesc('date_reservation')->take(3)->values();

        // Récupérer les activités récentes (3 dernières réservations de tests)
        $activitesRecentesTests = collect([]);
        if (!empty($reservationsTests)) {
            $activitesRecentesTests = $reservationsTests->sortByDesc('date_reservation')->take(2)->values();
        }

        // Récupérer les prochains tests à venir
        $prochainsTests = collect([]);
        if (!empty($reservationsTests)) {
            $prochainsTests = $reservationsTests
                ->filter(function ($reservation) {
                    return $reservation->testPsycho &&
                           $reservation->testPsycho->date > now() &&
                           $reservation->statut !== 'annulée';
                })
                ->sortBy(function ($reservation) {
                    return $reservation->testPsycho->date;
                })
                ->take(3)
                ->values();
        }

        return Inertia::render('Client/Dashboard', [
            'stats' => [
                'reservationsEnCours' => $reservationsEnCours,
                'reservationsTestEnCours'=> $reservationsTestEnCours,
                'stagesAVenir' => $stagesAVenir,
                'testsCount' => $testsCount,
                'testsConfirmes' => $testsConfirmes,
                'testsEnAttente' => $testsEnAttente,
                'testsAnnules' => $testsAnnules,
                'testsAVenir' => $testsAVenir,
                'totalPaiements' => $totalPaiements,
            ],
            'activitesRecentes' => $activitesRecentesStages,
            'activitesRecentesTests' => $activitesRecentesTests,
            'prochainsTests' => $prochainsTests,
        ]);
    }
}
