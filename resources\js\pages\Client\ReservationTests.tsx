import { Head } from '@inertiajs/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import ClientLayout from '@/layouts/client-layout';
import { ReservationTestPsycho } from '@/types';
import { format, parseISO } from 'date-fns';
import { fr } from 'date-fns/locale';
import { Badge } from '@/components/ui/badge';
import { CalendarDays, MapPin, Clock, FileText, Euro, Brain } from 'lucide-react';

interface ReservationTestsProps {
  reservations: ReservationTestPsycho[];
}

export default function ReservationTests({ reservations = [] }: ReservationTestsProps) {
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'confirmée':
        return <Badge className="bg-green-500">Confirmée</Badge>;
      case 'en attente':
        return <Badge className="bg-yellow-500">En attente</Badge>;
      case 'annulée':
        return <Badge className="bg-red-500"><PERSON><PERSON><PERSON></Badge>;
      default:
        return <Badge>{status}</Badge>;
    }
  };

  return (
    <ClientLayout title="Mes réservations de tests psychotechniques">
      <Head title="Mes réservations de tests psychotechniques" />

      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-bold tracking-tight">Mes réservations de tests psychotechniques</h1>
        </div>

        {reservations.length === 0 ? (
          <Card>
            <CardContent className="py-10 text-center">
              <p className="text-muted-foreground">Vous n'avez pas encore de réservations de tests psychotechniques.</p>
            </CardContent>
          </Card>
        ) : (
          <div className="space-y-4">
            {reservations.map((reservation) => {
              const testDate = reservation.test_psycho?.date
                ? parseISO(reservation.test_psycho.date)
                : new Date();

              return (
                <Card key={reservation.id} className="overflow-hidden">
                  <CardHeader className="pb-2">
                    <div className="flex items-center justify-between">
                      <CardTitle>
                        Test du {reservation.test_psycho?.date
                          ? format(parseISO(reservation.test_psycho.date), 'dd MMMM yyyy', { locale: fr })
                          : 'N/A'}
                      </CardTitle>
                      {getStatusBadge(reservation.statut)}
                    </div>
                    <CardDescription>
                      {reservation.type_test_psycho?.nom || 'Type de test non spécifié'}
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="grid gap-4">
                      <div className="flex items-center gap-2">
                        <CalendarDays className="h-4 w-4 text-muted-foreground" />
                        <span>
                          Date du test: {reservation.test_psycho?.date
                            ? format(parseISO(reservation.test_psycho.date), 'dd/MM/yyyy', { locale: fr })
                            : 'N/A'}
                        </span>
                      </div>

                      <div className="flex items-center gap-2">
                        <Clock className="h-4 w-4 text-muted-foreground" />
                        <span>
                          Heure: {reservation.test_psycho?.heure || 'N/A'}
                        </span>
                      </div>

                      <div className="flex items-center gap-2">
                        <MapPin className="h-4 w-4 text-muted-foreground" />
                        <span>
                          {reservation.test_psycho?.lieu?.nom || 'Lieu non spécifié'} - {reservation.test_psycho?.lieu?.ville?.nom || ''}
                        </span>
                      </div>

                      <div className="flex items-center gap-2">
                        <Brain className="h-4 w-4 text-muted-foreground" />
                        <span>
                          Type: {reservation.type_test_psycho?.nom || 'Non spécifié'}
                        </span>
                      </div>

                      <div className="flex items-center gap-2">
                        <Euro className="h-4 w-4 text-muted-foreground" />
                        <span>
                          Prix: {reservation.test_psycho?.prix ? `${reservation.test_psycho.prix} €` : 'N/A'}
                        </span>
                      </div>

                      {reservation.motif && (
                        <div className="flex items-center gap-2">
                          <FileText className="h-4 w-4 text-muted-foreground" />
                          <span>
                            Motif: {reservation.motif}
                          </span>
                        </div>
                      )}

                      {reservation.methode_paiement && (
                        <div className="flex items-center gap-2">
                          <FileText className="h-4 w-4 text-muted-foreground" />
                          <span>
                            Méthode de paiement: {reservation.methode_paiement}
                          </span>
                        </div>
                      )}

                      <div className="flex items-center gap-2">
                        <CalendarDays className="h-4 w-4 text-muted-foreground" />
                        <span>
                          Réservé le: {format(parseISO(reservation.date_reservation), 'dd/MM/yyyy', { locale: fr })}
                        </span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        )}
      </div>
    </ClientLayout>
  );
}
