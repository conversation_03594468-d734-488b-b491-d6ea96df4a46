import { Head, router } from '@inertiajs/react';
import DataTable from '@/components/DataTable';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { useState } from 'react';
import { useForm } from '@inertiajs/react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import AppLayout from '@/layouts/app-layout';
import { PaginatedData } from '@/types';

interface TypeStage {
    id: number;
    nom: string;
    description: string | null;
    reservations_count?: number;
}

interface BreadcrumbItem {
    title: string;
    href: string;
}

interface TypesStagesPageProps {
    types: PaginatedData<TypeStage>;
    auth: {
        user: {
            id: number;
            nom: string;
            prenom: string;
            email: string;
            role: string;
        };
    };
}

export default function Index({ types }: TypesStagesPageProps) {
    const [isOpen, setIsOpen] = useState(false);
    const [editingTypeStage, setEditingTypeStage] = useState<TypeStage | null>(null);

    const form = useForm({
        nom: '',
        description: '',
    });

    const columns = [
        { key: 'nom', label: 'Nom' },
        { key: 'description', label: 'Description' },
        {
            key: 'reservations_count',
            label: 'Réservations',
            render: (value: unknown) => (value || 0) as React.ReactNode,
        },
    ];

    const handleAdd = () => {
        setEditingTypeStage(null);
        form.reset();
        setIsOpen(true);
    };

    const handleEdit = (row: Record<string, unknown>) => {
        const typeStage = row as unknown as TypeStage;
        setEditingTypeStage(typeStage);
        form.setData({
            nom: typeStage.nom,
            description: typeStage.description || '',
        });
        setIsOpen(true);
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        if (editingTypeStage) {
            form.put(route('admin.types-stages.update', editingTypeStage.id), {
                onSuccess: () => setIsOpen(false),
            });
        } else {
            form.post(route('admin.types-stages.store'), {
                onSuccess: () => setIsOpen(false),
            });
        }
    };

    const handleDelete = (row: Record<string, unknown>) => {
        const typeStage = row as unknown as TypeStage;
        if (confirm('Êtes-vous sûr de vouloir supprimer ce type de stage ?')) {
            router.delete(route('admin.types-stages.destroy', typeStage.id));
        }
    };

    const breadcrumbs: BreadcrumbItem[] = [
        {
            title: 'Types de stages',
            href: '/admin/types-stages',
        },
    ];

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Types de stages" />

            <div className="flex h-full flex-1 flex-col gap-4 rounded-xl p-4">
                <DataTable
                    title="Types de stages"
                    columns={columns}
                    data={types.data.map(typeStage => ({
                        id: typeStage.id,
                        nom: typeStage.nom,
                        description: typeStage.description,
                        reservations_count: typeStage.reservations_count,
                    }))}
                    onAdd={handleAdd}
                    onEdit={handleEdit}
                    onDelete={handleDelete}
                    pagination={{
                        links: types.links,
                        from: types.from,
                        to: types.to,
                        total: types.total
                    }}
                />
            </div>
            <Dialog open={isOpen} onOpenChange={setIsOpen}>
                <DialogContent>
                    <DialogHeader>
                        <DialogTitle>
                            {editingTypeStage ? 'Modifier le type de stage' : 'Ajouter un type de stage'}
                        </DialogTitle>
                    </DialogHeader>
                    <form onSubmit={handleSubmit} className="space-y-4">
                        <div>
                            <Input
                                placeholder="Nom du type de stage"
                                value={form.data.nom}
                                onChange={e => form.setData('nom', e.target.value)}
                            />
                        </div>
                        <div>
                            <Textarea
                                placeholder="Description du type de stage"
                                value={form.data.description}
                                onChange={e => form.setData('description', e.target.value)}
                                rows={4}
                            />
                        </div>
                        <div className="flex justify-end space-x-2">
                            <Button
                                type="button"
                                variant="outline"
                                onClick={() => setIsOpen(false)}
                            >
                                Annuler
                            </Button>
                            <Button type="submit" disabled={form.processing}>
                                {editingTypeStage ? 'Modifier' : 'Ajouter'}
                            </Button>
                        </div>
                    </form>
                </DialogContent>
            </Dialog>
        </AppLayout>
    );
}
