<?php

namespace App\Services;

use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Exception;

class EmailService
{
    /**
     * Rate limiting cache key prefix
     */
    private const RATE_LIMIT_PREFIX = 'email_rate_limit:';

    /**
     * Send email with rate limiting and error handling
     */
    public function sendEmail(string $to, string $subject, string $view, array $data = [], array $options = [])
    {
        try {
            // Check rate limiting
            if (!$this->checkRateLimit($to)) {
                throw new Exception('Rate limit exceeded for email address: ' . $to);
            }

            // Validate email address
            if (!filter_var($to, FILTER_VALIDATE_EMAIL)) {
                throw new Exception('Invalid email address: ' . $to);
            }

            // Send email
            Mail::send($view, $data, function ($message) use ($to, $subject, $options) {
                $message->to($to)
                        ->subject($subject);

                // Add optional headers
                if (isset($options['reply_to'])) {
                    $message->replyTo($options['reply_to']);
                }

                if (isset($options['cc'])) {
                    $message->cc($options['cc']);
                }

                if (isset($options['bcc'])) {
                    $message->bcc($options['bcc']);
                }

                // Add security headers
                $message->getHeaders()->addTextHeader('X-Mailer', 'Laravel');
                $message->getHeaders()->addTextHeader('X-Priority', '3');
            });

            // Update rate limiting counter
            $this->updateRateLimit($to);

            Log::info('Email sent successfully', [
                'to' => $to,
                'subject' => $subject,
                'view' => $view
            ]);

            return true;

        } catch (Exception $e) {
            Log::error('Failed to send email', [
                'to' => $to,
                'subject' => $subject,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            throw $e;
        }
    }

    /**
     * Send raw email
     */
    public function sendRawEmail(string $to, string $subject, string $message, array $options = [])
    {
        try {
            if (!$this->checkRateLimit($to)) {
                throw new Exception('Rate limit exceeded for email address: ' . $to);
            }

            Mail::raw($message, function ($mail) use ($to, $subject, $options) {
                $mail->to($to)
                     ->subject($subject);

                if (isset($options['reply_to'])) {
                    $mail->replyTo($options['reply_to']);
                }
            });

            $this->updateRateLimit($to);

            Log::info('Raw email sent successfully', [
                'to' => $to,
                'subject' => $subject
            ]);

            return true;

        } catch (Exception $e) {
            Log::error('Failed to send raw email', [
                'to' => $to,
                'subject' => $subject,
                'error' => $e->getMessage()
            ]);

            throw $e;
        }
    }

    /**
     * Check rate limiting for email address
     */
    private function checkRateLimit(string $email): bool
    {
        $key = self::RATE_LIMIT_PREFIX . $email;
        $count = Cache::get($key, 0);
        $maxPerHour = config('mail-security.rate_limiting.max_emails_per_hour', 100);

        return $count < $maxPerHour;
    }

    /**
     * Update rate limiting counter
     */
    private function updateRateLimit(string $email): void
    {
        $key = self::RATE_LIMIT_PREFIX . $email;
        $count = Cache::get($key, 0);
        Cache::put($key, $count + 1, now()->addHour());
    }

    /**
     * Test email configuration
     */
    public function testConfiguration(): array
    {
        $results = [];

        try {
            // Test SMTP connection
            $transport = Mail::getSwiftMailer()->getTransport();
            $transport->start();
            $results['smtp_connection'] = 'OK';
        } catch (Exception $e) {
            $results['smtp_connection'] = 'FAILED: ' . $e->getMessage();
        }

        // Check configuration
        $results['mail_driver'] = config('mail.default');
        $results['mail_host'] = config('mail.mailers.smtp.host');
        $results['mail_port'] = config('mail.mailers.smtp.port');
        $results['mail_encryption'] = config('mail.mailers.smtp.encryption');
        $results['from_address'] = config('mail.from.address');

        return $results;
    }
}
