<?php

return [
    'mode'    => env('SUMUP_MODE', 'sandbox'), // Can be 'sandbox' or 'live'
    'sandbox' => [
        'app_id'     => env('SUMUP_SANDBOX_APP_ID', 'CCCSGKPFL'),
        'app_secret' => env('SUMUP_SANDBOX_APP_SECRET', 'cc_sk_classic_oD3xSBT7u31wIV68aCD2PPIBt26ITw7SvXPMCuJVXYMm4B3bGP'),
        'scopes'     => ['payments', 'transactions.history', 'user.app-settings', 'user.profile_readonly'],
    ],
    'live' => [
        'app_id'     => env('SUMUP_LIVE_APP_ID', ''),
        'app_secret' => env('SUMUP_LIVE_APP_SECRET', ''),
        'scopes'     => ['payments', 'transactions.history', 'user.app-settings', 'user.profile_readonly'],
    ],
    'currency'       => env('SUMUP_CURRENCY', 'EUR'),
    'return_url'     => env('SUMUP_RETURN_URL', ''),
    'checkout_url'   => env('SUMUP_CHECKOUT_URL', 'https://api.sumup.com/v0.1/checkouts'),
    'validate_ssl'   => env('SUMUP_VALIDATE_SSL', true),
    'access_token'   => env('SUMUP_ACCESS_TOKEN', ''),
    'refresh_token'  => env('SUMUP_REFRESH_TOKEN', ''),
];
