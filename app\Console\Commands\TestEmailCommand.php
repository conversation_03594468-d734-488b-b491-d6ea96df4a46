<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;
use Exception;

class TestEmailCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'email:test {email} {--subject=Test Email} {--message=This is a test email from <PERSON><PERSON>}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test email configuration by sending a test email';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $email = $this->argument('email');
        $subject = $this->option('subject');
        $message = $this->option('message');

        $this->info('Testing email configuration...');
        $this->info("Sending test email to: {$email}");

        try {
            // Display current mail configuration
            $this->displayMailConfig();

            // Send test email
            Mail::raw($message, function ($mail) use ($email, $subject) {
                $mail->to($email)
                     ->subject($subject);
            });

            $this->info('✅ Test email sent successfully!');
            $this->info('Please check the recipient\'s inbox (and spam folder).');

        } catch (Exception $e) {
            $this->error('❌ Failed to send test email:');
            $this->error($e->getMessage());
            
            Log::error('Email test failed', [
                'error' => $e->getMessage(),
                'email' => $email,
                'trace' => $e->getTraceAsString()
            ]);

            $this->info('Check the Laravel logs for more details: storage/logs/laravel.log');
        }
    }

    private function displayMailConfig()
    {
        $this->info('Current Mail Configuration:');
        $this->table(
            ['Setting', 'Value'],
            [
                ['MAIL_MAILER', config('mail.default')],
                ['MAIL_HOST', config('mail.mailers.smtp.host')],
                ['MAIL_PORT', config('mail.mailers.smtp.port')],
                ['MAIL_ENCRYPTION', config('mail.mailers.smtp.encryption')],
                ['MAIL_USERNAME', config('mail.mailers.smtp.username') ? '***' . substr(config('mail.mailers.smtp.username'), -10) : 'Not set'],
                ['MAIL_FROM_ADDRESS', config('mail.from.address')],
                ['MAIL_FROM_NAME', config('mail.from.name')],
            ]
        );
    }
}
