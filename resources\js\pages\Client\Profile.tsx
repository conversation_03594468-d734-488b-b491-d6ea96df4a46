import { Head, useForm, usePage } from '@inertiajs/react';
import { FormEventHandler } from 'react';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { type SharedData } from '@/types';
import ClientLayout from '@/layouts/client-layout';
import InputError from '@/components/input-error';
import { LoaderCircle } from 'lucide-react';

type ProfileForm = {
  civilite: string;
  nom: string;
  prenom: string;
  email: string;
  date_naissance: string;
  lieu_naissance: string;
  ville: string;
  code_postal: string;
  adresse: string;
  mobile: string;
  tel: string;
  num_permis: string;
  date_permis: string;
  lieu_permis: string;
}

export default function Profile() {
  const { auth } = usePage<SharedData>().props;
  const user = auth.user;

  const { data, setData, patch, errors, processing, recentlySuccessful } = useForm<ProfileForm>({
    civilite: user.civilite || '',
    nom: user.nom || '',
    prenom: user.prenom || '',
    email: user.email || '',
    date_naissance: user.date_naissance ? new Date(user.date_naissance).toISOString().split('T')[0] : '',
    lieu_naissance: user.lieu_naissance || '',
    ville: user.ville || '',
    code_postal: user.code_postal || '',
    adresse: user.adresse || '',
    mobile: user.mobile || '',
    tel: user.tel || '',
    num_permis: user.num_permis || '',
    date_permis: user.date_permis ? new Date(user.date_permis).toISOString().split('T')[0] : '',
    lieu_permis: user.lieu_permis || '',
  });

  const submit: FormEventHandler = (e) => {
    e.preventDefault();
    patch(route('client.profile.update'));
  };

  return (
    <ClientLayout title="Mon profil">
      <Head title="Mon profil" />
      
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-bold tracking-tight">Mon profil</h1>
        </div>

        <form onSubmit={submit}>
          <Card>
            <CardHeader>
              <CardTitle>Informations personnelles</CardTitle>
              <CardDescription>
                Mettez à jour vos informations personnelles
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <Label htmlFor="civilite">Civilité</Label>
                  <Select 
                    value={data.civilite} 
                    onValueChange={(value) => setData('civilite', value)}
                  >
                    <SelectTrigger id="civilite">
                      <SelectValue placeholder="Sélectionner une civilité" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Monsieur">Monsieur</SelectItem>
                      <SelectItem value="Madame">Madame</SelectItem>
                      <SelectItem value="Mademoiselle">Mademoiselle</SelectItem>
                    </SelectContent>
                  </Select>
                  <InputError message={errors.civilite} />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="nom">Nom</Label>
                  <Input
                    id="nom"
                    value={data.nom}
                    onChange={(e) => setData('nom', e.target.value)}
                  />
                  <InputError message={errors.nom} />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="prenom">Prénom</Label>
                  <Input
                    id="prenom"
                    value={data.prenom}
                    onChange={(e) => setData('prenom', e.target.value)}
                  />
                  <InputError message={errors.prenom} />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="email">Email</Label>
                  <Input
                    id="email"
                    type="email"
                    value={data.email}
                    onChange={(e) => setData('email', e.target.value)}
                  />
                  <InputError message={errors.email} />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="date_naissance">Date de naissance</Label>
                  <Input
                    id="date_naissance"
                    type="date"
                    value={data.date_naissance}
                    onChange={(e) => setData('date_naissance', e.target.value)}
                  />
                  <InputError message={errors.date_naissance} />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="lieu_naissance">Lieu de naissance</Label>
                  <Input
                    id="lieu_naissance"
                    value={data.lieu_naissance}
                    onChange={(e) => setData('lieu_naissance', e.target.value)}
                  />
                  <InputError message={errors.lieu_naissance} />
                </div>
              </div>

              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <Label htmlFor="ville">Ville</Label>
                  <Input
                    id="ville"
                    value={data.ville}
                    onChange={(e) => setData('ville', e.target.value)}
                  />
                  <InputError message={errors.ville} />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="code_postal">Code postal</Label>
                  <Input
                    id="code_postal"
                    value={data.code_postal}
                    onChange={(e) => setData('code_postal', e.target.value)}
                    maxLength={5}
                  />
                  <InputError message={errors.code_postal} />
                </div>
                <div className="space-y-2 md:col-span-2">
                  <Label htmlFor="adresse">Adresse</Label>
                  <Input
                    id="adresse"
                    value={data.adresse}
                    onChange={(e) => setData('adresse', e.target.value)}
                  />
                  <InputError message={errors.adresse} />
                </div>
              </div>

              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <Label htmlFor="mobile">Téléphone mobile</Label>
                  <Input
                    id="mobile"
                    value={data.mobile}
                    onChange={(e) => setData('mobile', e.target.value)}
                    maxLength={10}
                  />
                  <InputError message={errors.mobile} />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="tel">Téléphone fixe</Label>
                  <Input
                    id="tel"
                    value={data.tel}
                    onChange={(e) => setData('tel', e.target.value)}
                    maxLength={10}
                  />
                  <InputError message={errors.tel} />
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button type="submit" disabled={processing}>
                {processing && <LoaderCircle className="mr-2 h-4 w-4 animate-spin" />}
                Enregistrer
              </Button>
              {recentlySuccessful && (
                <p className="text-sm text-green-600">Informations mises à jour avec succès.</p>
              )}
            </CardFooter>
          </Card>

          <Card className="mt-6">
            <CardHeader>
              <CardTitle>Informations du permis</CardTitle>
              <CardDescription>
                Mettez à jour les informations de votre permis
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <Label htmlFor="num_permis">Numéro de permis</Label>
                  <Input
                    id="num_permis"
                    value={data.num_permis}
                    onChange={(e) => setData('num_permis', e.target.value)}
                  />
                  <InputError message={errors.num_permis} />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="date_permis">Date d'obtention</Label>
                  <Input
                    id="date_permis"
                    type="date"
                    value={data.date_permis}
                    onChange={(e) => setData('date_permis', e.target.value)}
                  />
                  <InputError message={errors.date_permis} />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="lieu_permis">Lieu d'obtention</Label>
                  <Input
                    id="lieu_permis"
                    value={data.lieu_permis}
                    onChange={(e) => setData('lieu_permis', e.target.value)}
                  />
                  <InputError message={errors.lieu_permis} />
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button type="submit" disabled={processing}>
                {processing && <LoaderCircle className="mr-2 h-4 w-4 animate-spin" />}
                Enregistrer
              </Button>
              {recentlySuccessful && (
                <p className="text-sm text-green-600">Informations mises à jour avec succès.</p>
              )}
            </CardFooter>
          </Card>
        </form>
      </div>
    </ClientLayout>
  );
}
