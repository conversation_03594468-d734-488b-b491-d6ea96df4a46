import FrontLayout from '@/layouts/front-layout';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Link } from '@inertiajs/react';
import { HeroSection } from '@/components/hero-section-dark';
import { Phone, Check } from "lucide-react";
import CountAnimation from '@/components/ui/count-animation';

export default function StageFrancePermis() {
    return (
        <FrontLayout title="Prix et inscription">
            <HeroSection
                subtitle={{
                    regular: "PRIX ET INSCRIPTION : ",
                    gradient: "Le prix du stage est de 189€ à 250€ en fonction des lieux"
                }}
                className="mb-8"
                gridOptions={{
                    angle: 65,
                    cellSize: 60,
                    opacity: 0.3,
                    lightLineColor: "#b60062",
                    darkLineColor: "#b60062"
                }}
            >
                <div className="flex flex-wrap gap-4 mt-6 justify-center">
                    <Button size="lg" asChild>
                        <Link href={route('stages')}>
                            Trouver un stage
                        </Link>
                    </Button>
                    <Button variant="outline" size="lg" asChild>
                        <Link href={route('sensibilisation-accidents')}>
                            Voir le programme
                        </Link>
                    </Button>
                </div>
            </HeroSection>

            <div className="container mx-auto px-4 py-8">
                <Card className="mb-8">
                    <CardHeader>
                        <CardTitle>Inscription en ligne</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                        <ul className="list-disc pl-5 space-y-2">
                            <li>À partir de la carte des stages, sélectionnez votre région. Choisissez votre stage et cliquez sur "réservation". Remplissez le formulaire et choisissez votre mode de règlement (CB, chèque ou mandat).</li>
                            <li>Pour un paiement par CB : vous recevrez une confirmation par email à imprimer et à présenter le jour du stage.</li>
                            <li>Pour un paiement par chèque ou mandat : vous recevrez une fiche d'inscription à nous retourner avec votre règlement sous 6 jours.</li>
                            <li>Passé ce délai, nous nous réservons le droit de libérer votre place.</li>
                        </ul>
                    </CardContent>
                </Card>

                <div className="grid md:grid-cols-3 gap-8 mb-8">
                    <div className="h-full">
                        <img
                            src="/images/tell.jpg"
                            alt="Contact stage permis"
                            className="w-full h-full object-cover rounded-lg"
                        />
                    </div>

                    <Card className="md:col-span-2">
                        <CardHeader>
                            <CardTitle className="text-center gap-2 text-primary uppercase">
                                Notre numéro de téléphone
                            </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-6">
                            <div className="items-center justify-center gap-x-3 space-y-3 sm:flex sm:space-y-0">
                                <span className="relative inline-block overflow-hidden rounded-full p-[1.5px]">
                                    <span className="absolute inset-[-1000%] animate-[spin_2s_linear_infinite] bg-[conic-gradient(from_90deg_at_50%_50%,#b60062_0%,#FF69B4_50%,#b60062_100%)]" />
                                    <div className="inline-flex h-full w-full cursor-pointer items-center justify-center rounded-full bg-white dark:bg-gray-950 text-xs font-medium backdrop-blur-3xl">
                                        <a
                                            href="tel:0658772385"
                                            className="text-4xl inline-flex rounded-full text-center group items-center w-full justify-center bg-gradient-to-tr from-white-100/80 via-primary/30 to-transparent
                                                dark:from-zinc-300/5 dark:via-primary/20 text-primary dark:text-white border-input border-[1px]
                                                hover:bg-gradient-to-tr hover:from-zinc-300/30 hover:via-primary/40 hover:to-transparent dark:hover:from-zinc-300/10
                                                dark:hover:via-primary/30 transition-all sm:w-auto py-4 px-10">
                                            06 58 77 23 85
                                        </a>
                                    </div>
                                </span>
                            </div>
                            <ul className="space-y-3">
                                {[
                                    "Nous vous envoyons par courrier ou par mail votre fiche d'inscription au stage que vous avez choisi",
                                    "Votre place est réservée pendant 8 jours ouvrables",
                                    "Passé ce délai, nous nous réservons le droit d'ouvrir à nouveau la place à la réservation"
                                ].map((item, i) => (
                                    <li key={i} className="flex gap-2">
                                        <Check className="h-5 w-5 text-primary flex-shrink-0" />
                                        <span>{item}</span>
                                    </li>
                                ))}
                            </ul>
                            <div className="grid grid-cols-3 gap-12 mx-10 mt-15">
                                <div className="relative overflow-hidden rounded-2xl bg-gradient-to-b from-background to-background/80 p-6 shadow-lg border border-border/50 backdrop-blur-sm">
                                    <div className="absolute inset-0 bg-gradient-to-b from-primary/10 to-transparent dark:from-primary/5" />
                                    <div className="relative flex flex-col items-center justify-center gap-2 text-center">
                                        <CountAnimation
                                            number={98397}
                                            className="text-4xl font-bold bg-gradient-to-r from-primary to-primary/80 bg-clip-text text-transparent tracking-tighter"
                                        />
                                        <p className="font-medium text-primary text-md">clients satisfaits</p>
                                    </div>
                                </div>

                                <div className="relative overflow-hidden rounded-2xl bg-gradient-to-b from-background to-background/80 p-6 shadow-lg border border-border/50 backdrop-blur-sm">
                                    <div className="absolute inset-0 bg-gradient-to-b from-primary/10 to-transparent dark:from-primary/5" />
                                    <div className="relative flex flex-col items-center justify-center gap-2 text-center">
                                        <CountAnimation
                                            number={393588}
                                            className="text-4xl font-bold bg-gradient-to-r from-primary to-primary/80 bg-clip-text text-transparent tracking-tighter"
                                        />
                                        <p className="font-medium text-primary text-md">points récupérés</p>
                                    </div>
                                </div>

                                <div className="relative overflow-hidden rounded-2xl bg-gradient-to-b from-background to-background/80 p-6 shadow-lg border border-border/50 backdrop-blur-sm">
                                    <div className="absolute inset-0 bg-gradient-to-b from-primary/10 to-transparent dark:from-primary/5" />
                                    <div className="relative flex flex-col items-center justify-center gap-2 text-center">
                                        <CountAnimation
                                            number={245992}
                                            className="text-4xl font-bold bg-gradient-to-r from-primary to-primary/80 bg-clip-text text-transparent tracking-tighter"
                                        />
                                        <p className="font-medium text-primary text-md">heures de travail</p>
                                    </div>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                </div>

                <Card>
                    <CardHeader>
                        <CardTitle>Confirmation de votre inscription</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                        <ul className="list-disc pl-5 space-y-2">
                            <li>Votre inscription est définitivement validée à réception du dossier complet. Une confirmation vous sera envoyée par email ou courrier.</li>
                            <li>Les chèques ne sont encaissés que 7 jours avant le début de la formation.</li>
                            <li>En cas d'absence non signalée au moins 7 jours avant, le prix de la formation reste dû.</li>
                            <li>Pour toute question, contactez-nous au 06 58 77 23 85 (8h-20h en semaine, 9h-13h le samedi).</li>
                        </ul>
                    </CardContent>
                </Card>
            </div>
        </FrontLayout>
    );
}





