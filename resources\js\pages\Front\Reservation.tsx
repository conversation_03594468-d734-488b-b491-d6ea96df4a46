import { useState, FormEventHand<PERSON> } from 'react';
import { Head, useForm } from '@inertiajs/react';
import FrontLayout from '@/layouts/front-layout';
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { format, parseISO } from 'date-fns';
import { fr } from 'date-fns/locale';
import { Calendar, MapPin, Euro, User, FileText } from 'lucide-react';
import InputError from '@/components/input-error';
import { Separator } from '@/components/ui/separator';

interface ReservationProps {
  stage: {
    id: number;
    reference: string;
    date_debut: string;
    date_fin: string;
    places_disponibles: number;
    prix: number;
    lieu: {
      nom: string;
      ville: {
        nom: string;
        departement: {
          nom: string;
        };
      };
    };
  };
  user: {
    id: number;
    nom: string;
    prenom: string;
    email: string;
  };
}

export default function Reservation({ stage, user }: ReservationProps) {
  const [selectedCase, setSelectedCase] = useState<string | null>(null);

  const { data, setData, post, processing, errors } = useForm({
    stage_id: stage.id,
    cas: '',
    date_infr: '',
    heure_infr: '',
    lieu_infr: '',
    recto: null as File | null,
    verso: null as File | null,
    recto48: null as File | null,
    verso48: null as File | null,
  });

  const handleCasChange = (value: string) => {
    setSelectedCase(value);
    setData('cas', value);
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>, field: string) => {
    if (e.target.files && e.target.files[0]) {
      setData(field as keyof typeof data, e.target.files[0]);
    }
  };

  const submit: FormEventHandler = (e) => {
    e.preventDefault();
    post(route('reservation.store'));
  };

  return (
    <FrontLayout title="Réservation de stage">
      <Head title="Réservation de stage" />
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8 flex items-center">
          <div className="flex items-center gap-2">
            <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary text-white">1</div>
            <span className="font-medium">Authentification</span>
          </div>
          <div className="mx-2 h-px w-8 bg-gray-300"></div>
          <div className="flex items-center gap-2">
            <div className="flex h-8 w-8 items-center justify-center rounded-full bg-gray-200 text-gray-700">2</div>
            <span className="text-gray-700">Paiement</span>
          </div>
          <div className="mx-2 h-px w-8 bg-gray-300"></div>
          <div className="flex items-center gap-2">
            <div className="flex h-8 w-8 items-center justify-center rounded-full bg-gray-200 text-gray-700">3</div>
            <span className="text-gray-700">Confirmation</span>
          </div>
        </div>

        <div className="grid gap-8 md:grid-cols-3">
          <div className="md:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle>Formulaire de réservation</CardTitle>
                <CardDescription>
                  Veuillez remplir ce formulaire pour réserver votre stage
                </CardDescription>
              </CardHeader>
              <CardContent>
                <form onSubmit={submit} className="space-y-6">
                  <input type="hidden" name="stage_id" value={stage.id} />

                  <div>
                    <h3 className="mb-4 text-lg font-medium">Type de stage</h3>
                    <RadioGroup value={data.cas} onValueChange={handleCasChange} className="space-y-3">
                      <div className="flex items-start space-x-2 rounded-md border p-3">
                        <RadioGroupItem value="1" id="cas1" />
                        <Label htmlFor="cas1" className="flex-1 cursor-pointer">
                          <span className="font-medium">Cas 1: Récupération volontaire de 4 points</span>
                        </Label>
                      </div>
                      <div className="flex items-start space-x-2 rounded-md border p-3">
                        <RadioGroupItem value="2" id="cas2" />
                        <Label htmlFor="cas2" className="flex-1 cursor-pointer">
                          <span className="font-medium">Cas 2: Stage en période probatoire (joindre lettre 48N)</span>
                        </Label>
                      </div>
                      <div className="flex items-start space-x-2 rounded-md border p-3">
                        <RadioGroupItem value="3" id="cas3" />
                        <Label htmlFor="cas3" className="flex-1 cursor-pointer">
                          <span className="font-medium">Cas 3: Alternative aux poursuites ou composition pénale</span>
                        </Label>
                      </div>
                      <div className="flex items-start space-x-2 rounded-md border p-3">
                        <RadioGroupItem value="4" id="cas4" />
                        <Label htmlFor="cas4" className="flex-1 cursor-pointer">
                          <span className="font-medium">Cas 4: Peine complémentaire ou mise à l'épreuve</span>
                        </Label>
                      </div>
                    </RadioGroup>
                    <InputError message={errors.cas} />
                  </div>

                  {selectedCase === '1' && (
                    <div className="rounded-md bg-muted p-4 space-y-4">
                      <h4 className="font-medium">Documents à fournir</h4>
                      <div className="space-y-4">
                        <div>
                          <Label htmlFor="recto">Télécharger votre permis de conduire en recto</Label>
                          <Input
                            id="recto"
                            type="file"
                            className="mt-1"
                            onChange={(e) => handleFileChange(e, 'recto')}
                          />
                          <p className="mt-1 text-sm text-muted-foreground">
                            Il faut que la copie soit claire et lisible
                          </p>
                          <InputError message={errors.recto} />
                        </div>
                        <div>
                          <Label htmlFor="verso">Télécharger votre permis de conduire en verso</Label>
                          <Input
                            id="verso"
                            type="file"
                            className="mt-1"
                            onChange={(e) => handleFileChange(e, 'verso')}
                          />
                          <p className="mt-1 text-sm text-muted-foreground">
                            Il faut que la copie soit claire et lisible
                          </p>
                          <InputError message={errors.verso} />
                        </div>
                      </div>
                    </div>
                  )}

                  {selectedCase === '2' && (
                    <div className="rounded-md bg-muted p-4 space-y-4">
                      <h4 className="font-medium">Stage en période probatoire (joindre lettre 48N)</h4>
                      <div className="grid gap-4 md:grid-cols-2">
                        <div>
                          <Label htmlFor="date_infr">Date infraction</Label>
                          <Input
                            id="date_infr"
                            type="date"
                            className="mt-1"
                            value={data.date_infr}
                            onChange={(e) => setData('date_infr', e.target.value)}
                          />
                          <InputError message={errors.date_infr} />
                        </div>
                        <div>
                          <Label htmlFor="heure_infr">Heure infraction</Label>
                          <Input
                            id="heure_infr"
                            type="time"
                            className="mt-1"
                            value={data.heure_infr}
                            onChange={(e) => setData('heure_infr', e.target.value)}
                          />
                          <InputError message={errors.heure_infr} />
                        </div>
                      </div>
                      <div>
                        <Label htmlFor="lieu_infr">Lieu infraction</Label>
                        <Input
                          id="lieu_infr"
                          type="text"
                          className="mt-1"
                          value={data.lieu_infr}
                          onChange={(e) => setData('lieu_infr', e.target.value)}
                        />
                        <InputError message={errors.lieu_infr} />
                      </div>
                      <div>
                        <Label htmlFor="recto48">Télécharger copie recto de votre lettre 48N</Label>
                        <Input
                          id="recto48"
                          type="file"
                          className="mt-1"
                          onChange={(e) => handleFileChange(e, 'recto48')}
                        />
                        <p className="mt-1 text-sm text-muted-foreground">
                          Il faut que la copie soit claire et lisible
                        </p>
                        <InputError message={errors.recto48} />
                      </div>
                      <div>
                        <Label htmlFor="verso48">Télécharger copie verso de votre lettre 48N</Label>
                        <Input
                          id="verso48"
                          type="file"
                          className="mt-1"
                          onChange={(e) => handleFileChange(e, 'verso48')}
                        />
                        <p className="mt-1 text-sm text-muted-foreground">
                          Il faut que la copie soit claire et lisible
                        </p>
                        <InputError message={errors.verso48} />
                      </div>
                    </div>
                  )}

                  {(selectedCase === '3' || selectedCase === '4') && (
                    <div className="rounded-md bg-muted p-4 space-y-4">
                      <h4 className="font-medium">
                        {selectedCase === '3'
                          ? 'Alternative aux poursuites ou composition pénale'
                          : 'Peine complémentaire ou mise à l\'épreuve'}
                      </h4>
                      <div className="grid gap-4 md:grid-cols-2">
                        <div>
                          <Label htmlFor="date_infr">Date infraction</Label>
                          <Input
                            id="date_infr"
                            type="date"
                            className="mt-1"
                            value={data.date_infr}
                            onChange={(e) => setData('date_infr', e.target.value)}
                          />
                          <InputError message={errors.date_infr} />
                        </div>
                        <div>
                          <Label htmlFor="heure_infr">Heure infraction</Label>
                          <Input
                            id="heure_infr"
                            type="time"
                            className="mt-1"
                            value={data.heure_infr}
                            onChange={(e) => setData('heure_infr', e.target.value)}
                          />
                          <InputError message={errors.heure_infr} />
                        </div>
                      </div>
                      <div>
                        <Label htmlFor="lieu_infr">Lieu infraction</Label>
                        <Input
                          id="lieu_infr"
                          type="text"
                          className="mt-1"
                          value={data.lieu_infr}
                          onChange={(e) => setData('lieu_infr', e.target.value)}
                        />
                        <InputError message={errors.lieu_infr} />
                      </div>
                    </div>
                  )}

                  <div className="flex justify-between">
                    <Button type="button" variant="outline" onClick={() => window.history.back()}>
                      Retour
                    </Button>
                    <Button type="submit" disabled={processing || !data.cas}>
                      Continuer vers le paiement
                    </Button>
                  </div>
                </form>
              </CardContent>
            </Card>
          </div>

          <div>
            <Card>
              <CardHeader>
                <CardTitle>Récapitulatif</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center gap-2">
                  <Calendar className="h-5 w-5 text-muted-foreground" />
                  <div>
                    <p className="font-medium">
                      {format(parseISO(stage.date_debut), 'dd MMMM yyyy', { locale: fr })}
                    </p>
                    <p className="text-sm text-muted-foreground">
                      au {format(parseISO(stage.date_fin), 'dd MMMM yyyy', { locale: fr })}
                    </p>
                  </div>
                </div>

                <div className="flex items-center gap-2">
                  <MapPin className="h-5 w-5 text-muted-foreground" />
                  <div>
                    <p className="font-medium">{stage.lieu.nom}</p>
                    <p className="text-sm text-muted-foreground">
                      {stage.lieu.ville.nom}, {stage.lieu.ville.departement.nom}
                    </p>
                  </div>
                </div>

                <div className="flex items-center gap-2">
                  <Euro className="h-5 w-5 text-muted-foreground" />
                  <p className="font-medium">{stage.prix} €</p>
                </div>

                <Separator />

                <div className="flex items-center gap-2">
                  <User className="h-5 w-5 text-muted-foreground" />
                  <div>
                    <p className="font-medium">{user.prenom} {user.nom}</p>
                    <p className="text-sm text-muted-foreground">{user.email}</p>
                  </div>
                </div>

                <div className="flex items-center gap-2">
                  <FileText className="h-5 w-5 text-muted-foreground" />
                  <p className="font-medium">Référence: {stage.reference}</p>
                </div>
              </CardContent>
              <CardFooter>
                <Button variant="outline" className="w-full" asChild>
                  <a href={route('client.profile')} target="_blank">Modifier mes coordonnées</a>
                </Button>
              </CardFooter>
            </Card>
          </div>
        </div>
      </div>
    </FrontLayout>
  );
}

