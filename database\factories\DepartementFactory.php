<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

class DepartementFactory extends Factory
{
    public function definition(): array
    {
        return [
            'nom' => fake()->unique()->randomElement([
                'Ain', 'Aisne', 'Allier', 'Alpes-de-Haute-Provence', 'Hautes-Alpes',
                'Alpes-Maritimes', 'Ardèche', 'Ardennes', 'Ariège', 'Aube',
                // Add more departments as needed
            ]),
            'code' => fake()->unique()->numberBetween(1, 95),
        ];
    }
}
