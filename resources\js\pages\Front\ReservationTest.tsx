import FrontLayout from '@/layouts/front-layout';
import { <PERSON>, <PERSON>Content, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useForm } from '@inertiajs/react';
import { useState } from 'react';
import { format, parseISO } from 'date-fns';
import { fr } from 'date-fns/locale';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { InfoIcon } from 'lucide-react';
import { TestPsycho, TypeTestPsycho, User } from '@/types';

interface ReservationTestProps {
  test: TestPsycho;
  typeTests: TypeTestPsycho[];
  user: User;
}

export default function ReservationTest({ test, typeTests }: ReservationTestProps) {
  const [selectedType, setSelectedType] = useState<string | null>(null);

  const { data, setData, post, processing, errors } = useForm({
    test_psycho_id: test.id,
    type_test_psycho_id: '',
    motif: '',
    permis_recto: null as File | null,
    permis_verso: null as File | null,
    document_tribunal: null as File | null,
  });

  const handleTypeChange = (value: string) => {
    setSelectedType(value);
    setData('type_test_psycho_id', value);
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>, field: string) => {
    if (e.target.files && e.target.files[0]) {
      setData(field as keyof typeof data, e.target.files[0]);
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    post(route('reservation-test.store'));
  };

  return (
    <FrontLayout title="Réservation de test psychotechnique">
      <div className="container mx-auto px-4 py-8">
        <h1 className="mb-6 text-3xl font-bold">Réservation de test psychotechnique</h1>

        <div className="grid gap-6 md:grid-cols-3">
          <div className="md:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle>Formulaire de réservation</CardTitle>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleSubmit} className="space-y-6">
                  <div>
                    <h3 className="text-lg font-medium mb-4">Type de test psychotechnique</h3>
                    <RadioGroup
                      value={data.type_test_psycho_id}
                      onValueChange={handleTypeChange}
                      className="space-y-3"
                    >
                      {typeTests.map((type) => (
                        <div key={type.id} className="flex items-start space-x-2">
                          <RadioGroupItem value={type.id.toString()} id={`type-${type.id}`} />
                          <div className="grid gap-1.5 leading-none">
                            <Label htmlFor={`type-${type.id}`} className="font-medium">
                              {type.nom}
                            </Label>
                            {type.description && (
                              <p className="text-sm text-muted-foreground">
                                {type.description}
                              </p>
                            )}
                          </div>
                        </div>
                      ))}
                    </RadioGroup>
                    {errors.type_test_psycho_id && (
                      <p className="text-sm text-red-500 mt-1">{errors.type_test_psycho_id}</p>
                    )}
                  </div>

                  <div>
                    <Label htmlFor="motif" className="text-lg font-medium mb-2 block">
                      Motif du test
                    </Label>
                    <Textarea
                      id="motif"
                      value={data.motif || ''}
                      onChange={(e) => setData('motif', e.target.value)}
                      placeholder="Précisez le motif de votre test psychotechnique"
                      className="min-h-[100px]"
                    />
                    {errors.motif && (
                      <p className="text-sm text-red-500 mt-1">{errors.motif}</p>
                    )}
                  </div>

                  <div className="space-y-4">
                    <h3 className="text-lg font-medium">Documents à fournir</h3>

                    <Alert>
                      <InfoIcon className="h-4 w-4" />
                      <AlertTitle>Important</AlertTitle>
                      <AlertDescription>
                        Veuillez fournir des copies lisibles de vos documents. Les formats acceptés sont PDF, JPG et PNG.
                      </AlertDescription>
                    </Alert>

                    <div>
                      <Label htmlFor="permis_recto" className="font-medium block mb-2">
                        Permis de conduire (recto)
                      </Label>
                      <Input
                        id="permis_recto"
                        type="file"
                        onChange={(e) => handleFileChange(e, 'permis_recto')}
                        accept=".pdf,.jpg,.jpeg,.png"
                      />
                      {errors.permis_recto && (
                        <p className="text-sm text-red-500 mt-1">{errors.permis_recto}</p>
                      )}
                    </div>

                    <div>
                      <Label htmlFor="permis_verso" className="font-medium block mb-2">
                        Permis de conduire (verso)
                      </Label>
                      <Input
                        id="permis_verso"
                        type="file"
                        onChange={(e) => handleFileChange(e, 'permis_verso')}
                        accept=".pdf,.jpg,.jpeg,.png"
                      />
                      {errors.permis_verso && (
                        <p className="text-sm text-red-500 mt-1">{errors.permis_verso}</p>
                      )}
                    </div>

                    <div>
                      <Label htmlFor="document_tribunal" className="font-medium block mb-2">
                        Document du tribunal ou de la préfecture
                      </Label>
                      <Input
                        id="document_tribunal"
                        type="file"
                        onChange={(e) => handleFileChange(e, 'document_tribunal')}
                        accept=".pdf,.jpg,.jpeg,.png"
                      />
                      {errors.document_tribunal && (
                        <p className="text-sm text-red-500 mt-1">{errors.document_tribunal}</p>
                      )}
                    </div>
                  </div>

                  <Button type="submit" className="w-full" disabled={processing}>
                    {processing ? 'Traitement en cours...' : 'Réserver et procéder au paiement'}
                  </Button>
                </form>
              </CardContent>
            </Card>
          </div>

          <div>
            <Card>
              <CardHeader>
                <CardTitle>Détails du test</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h3 className="font-semibold">Date</h3>
                  <p>{format(parseISO(test.date), 'dd MMMM yyyy', { locale: fr })}</p>
                </div>
                <div>
                  <h3 className="font-semibold">Heure</h3>
                  <p>{test.heure}</p>
                </div>
                <div>
                  <h3 className="font-semibold">Lieu</h3>
                  <p>{test.lieu?.nom}</p>
                  <p className="text-sm text-muted-foreground">
                    {test.lieu?.ville?.nom}, {test.lieu?.ville?.departement?.nom}
                  </p>
                </div>
                <div>
                  <h3 className="font-semibold">Prix</h3>
                  <p className="text-lg font-bold">{test.prix} €</p>
                </div>
                <div>
                  <h3 className="font-semibold">Référence</h3>
                  <p>{test.reference}</p>
                </div>
              </CardContent>
            </Card>

            <Card className="mt-4">
              <CardHeader>
                <CardTitle>Informations</CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="list-disc pl-5 space-y-2">
                  <li>Veuillez vous présenter 15 minutes avant l'heure du test</li>
                  <li>N'oubliez pas d'apporter vos documents originaux</li>
                  <li>Le test dure environ 1 heure</li>
                  <li>Les résultats vous seront communiqués à la fin du test</li>
                </ul>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </FrontLayout>
  );
}
