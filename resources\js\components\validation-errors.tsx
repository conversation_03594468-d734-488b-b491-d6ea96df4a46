import { cn } from '@/lib/utils';
import { type HTMLAttributes } from 'react';

export default function ValidationErrors({ 
  errors, 
  className = '', 
  ...props 
}: HTMLAttributes<HTMLDivElement> & { 
  errors?: string | string[] | Record<string, string | string[]> 
}) {
    if (!errors || (typeof errors === 'object' && Object.keys(errors).length === 0)) {
        return null;
    }

    const renderErrors = () => {
        if (typeof errors === 'string') {
            return <p className="text-sm text-red-600 dark:text-red-400">{errors}</p>;
        } else if (Array.isArray(errors)) {
            return errors.map((error, index) => (
                <p key={index} className="text-sm text-red-600 dark:text-red-400">{error}</p>
            ));
        } else {
            return Object.entries(errors).map(([key, value]) => {
                const errorMessages = Array.isArray(value) ? value : [value];
                return errorMessages.map((message, index) => (
                    <p key={`${key}-${index}`} className="text-sm text-red-600 dark:text-red-400">{message}</p>
                ));
            });
        }
    };

    return (
        <div {...props} className={cn('mt-2 space-y-1', className)}>
            {renderErrors()}
        </div>
    );
}