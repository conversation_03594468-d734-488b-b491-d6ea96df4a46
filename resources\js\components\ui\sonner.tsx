import { useAppearance } from "@/hooks/use-appearance"
import { Toaster as Son<PERSON> } from "sonner"
import { useEffect, useState } from "react"
import { cn } from "@/lib/utils"

type ToasterProps = React.ComponentProps<typeof Sonner>

export function Toaster({ ...props }: ToasterProps) {
  const { appearance } = useAppearance()
  const [theme, setTheme] = useState<"light" | "dark" | "system">("system")

  useEffect(() => {
    setTheme(appearance)
  }, [appearance])

  return (
    <Sonner
      theme={theme as ToasterProps["theme"]}
      className="toaster group"
      toastOptions={{
        classNames: {
            error: cn(
                "group-[.toaster]:!bg-red-500 group-[.toaster]:!text-white group-[.toaster]:!border-red-600"
            ),
            success: cn(
                "group-[.toaster]:!bg-green-500 group-[.toaster]:!text-white group-[.toaster]:!border-green-600"
            ),
            warning: cn(
                "group-[.toaster]:!bg-yellow-500 group-[.toaster]:!text-white group-[.toaster]:!border-yellow-600"
            ),
            info: cn(
                "group-[.toaster]:!bg-blue-500 group-[.toaster]:!text-white group-[.toaster]:!border-blue-600"
            ),
            toast: cn(
                "group toast group-[.toaster]:bg-background group-[.toaster]:text-foreground group-[.toaster]:border-border group-[.toaster]:shadow-lg"
            ),
            description: cn(
                "group-[.toast]:text-muted-foreground"
            ),
            actionButton: cn(
                "group-[.toast]:bg-primary group-[.toast]:text-primary-foreground"
            ),
            cancelButton: cn(
                "group-[.toast]:bg-muted group-[.toast]:text-muted-foreground"
            ),
        },
      }}
      {...props}
    />
  )
}
