import { Table } from "@tanstack/react-table"
import { ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight } from "lucide-react"
import { <PERSON> } from "@inertiajs/react"

import { Button } from "@/components/ui/button"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"

interface DataTablePaginationProps<TData> {
  table: Table<TData>
  pagination: {
    links: Array<{
      url: string | null
      label: string
      active: boolean
    }>
    from: number
    to: number
    total: number
  }
}

export function DataTablePagination<TData>({
  table,
  pagination,
}: DataTablePaginationProps<TData>) {
  // Clean up the pagination labels
  const cleanLinks = pagination.links.map(link => ({
    ...link,
    label: link.label
      .replace('Previous', '')
      .replace('Next', '')
      .replace(/^&laquo;\s*/, '«')
      .replace(/&raquo;$/, '»')
  }))

  return (
    <div className="flex flex-col items-center gap-4 sm:flex-row sm:justify-between">
      <div className="text-sm text-muted-foreground">
        Affichage des éléments {pagination.from} à {pagination.to} sur {pagination.total}
      </div>
      <div className="flex items-center gap-2">
        {cleanLinks.map((link, index) => {
          if (!link.url) {
            return (
              <Button
                key={index}
                variant="outline"
                size="icon"
                disabled
                className="h-8 w-8"
              >
                <span>{link.label}</span>
              </Button>
            )
          }

          return (
            <Button
              key={index}
              variant={link.active ? "default" : "outline"}
              size="icon"
              className="h-8 w-8"
              asChild
            >
              <Link
                href={link.url}
                preserveScroll
                preserveState
              >
                <span>{link.label}</span>
              </Link>
            </Button>
          )
        })}
      </div>
    </div>
  )
}
