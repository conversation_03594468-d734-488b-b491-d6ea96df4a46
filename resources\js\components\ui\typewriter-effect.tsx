import * as React from "react";

interface Word {
  text: string;
  className?: string;
}

interface TypewriterEffectProps {
  words: Word[];
  className?: string;
  onWordChange?: (index: number) => void;
}

// Browser detection hook
const useBrowserDetection = () => {
  const [isSafari, setIsSafari] = React.useState(false);

  React.useEffect(() => {
    if (typeof window !== 'undefined') {
      try {
        const userAgent = window.navigator.userAgent.toLowerCase();
        const safariDetected = (
          userAgent.includes('safari') &&
          !userAgent.includes('chrome') &&
          !userAgent.includes('chromium') &&
          !userAgent.includes('edge') &&
          !userAgent.includes('firefox')
        ) || (
          userAgent.includes('mobile') && userAgent.includes('safari') && !userAgent.includes('chrome')
        ) || (
          /version\/[\d.]+.*safari/i.test(userAgent) && !/chrome|chromium|edge|firefox/i.test(userAgent)
        );

        setIsSafari(safariDetected);
      } catch (error) {
        setIsSafari(false);
      }
    }
  }, []);

  return { isSafari };
};

export function TypewriterEffect({ words, className = "", onWordChange }: TypewriterEffectProps) {
  const { isSafari } = useBrowserDetection();
  const [displayedText, setDisplayedText] = React.useState("");
  const [wordIndex, setWordIndex] = React.useState(0);
  const [charIndex, setCharIndex] = React.useState(0);
  const [isDeleting, setIsDeleting] = React.useState(false);

  React.useEffect(() => {
    // Safari-compatible simplified behavior
    if (isSafari) {
      // For Safari, just cycle through words without typewriter effect
      const interval = setInterval(() => {
        const nextIndex = (wordIndex + 1) % words.length;
        setWordIndex(nextIndex);
        if (onWordChange) {
          onWordChange(nextIndex);
        }
      }, 3000); // Change word every 3 seconds

      return () => clearInterval(interval);
    }

    // Full typewriter effect for other browsers
    const currentWord = words[wordIndex].text;
    let timeout: NodeJS.Timeout;

    if (!isDeleting && charIndex <= currentWord.length) {
      setDisplayedText(currentWord.substring(0, charIndex));
      timeout = setTimeout(() => setCharIndex(charIndex + 1), 100);
    } else if (isDeleting && charIndex >= 0) {
      setDisplayedText(currentWord.substring(0, charIndex));
      timeout = setTimeout(() => setCharIndex(charIndex - 1), 50);
    } else if (!isDeleting && charIndex > currentWord.length) {
      timeout = setTimeout(() => setIsDeleting(true), 1000);
    } else if (isDeleting && charIndex < 0) {
      setIsDeleting(false);
      const nextIndex = (wordIndex + 1) % words.length;
      setWordIndex(nextIndex);

      // Utiliser un setTimeout pour éviter de mettre à jour l'état pendant le rendu
      if (onWordChange) {
        setTimeout(() => {
          onWordChange(nextIndex);
        }, 0);
      }
      setCharIndex(0);
    }

    return () => clearTimeout(timeout);
  }, [charIndex, isDeleting, wordIndex, words, onWordChange, isSafari]);

  return (
    <h1 className={`text-3xl font-bold md:text-5xl ${className}`}>
      {words[wordIndex].className ? (
        <span className={words[wordIndex].className}>
          {isSafari ? words[wordIndex].text : displayedText}
        </span>
      ) : (
        isSafari ? words[wordIndex].text : displayedText
      )}
      {!isSafari && <span className="animate-pulse">|</span>}
    </h1>
  );
}
