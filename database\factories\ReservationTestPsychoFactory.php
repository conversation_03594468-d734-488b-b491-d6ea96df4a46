<?php

namespace Database\Factories;

use App\Models\ReservationTestPsycho;
use App\Models\TestPsycho;
use App\Models\TypeTestPsycho;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

class ReservationTestPsychoFactory extends Factory
{
    protected $model = ReservationTestPsycho::class;

    public function definition(): array
    {
        return [
            'test_psycho_id' => TestPsycho::factory(),
            'user_id' => User::factory(),
            'type_test_psycho_id' => TypeTestPsycho::factory(),
            'date_reservation' => fake()->dateTimeBetween('-1 month', 'now'),
            'statut' => fake()->randomElement(['confirmée', 'en attente', 'annulée']),
            'motif' => fake()->sentence(),
            'permis_recto' => 'permis/recto/' . fake()->uuid() . '.pdf',
            'permis_verso' => 'permis/verso/' . fake()->uuid() . '.pdf',
            'document_tribunal' => 'tribunal/' . fake()->uuid() . '.pdf',
        ];
    }
}
