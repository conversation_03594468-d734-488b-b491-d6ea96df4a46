<?php

namespace App\Http\Controllers;

use App\Models\TestPsycho;
use App\Models\TypeTestPsycho;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;

class ReservationTestController extends Controller
{
    public function show($testId)
    {
        // Vérifier si l'utilisateur est connecté
        if (!Auth::check()) {
            // Stocker l'ID du test dans la session pour redirection après connexion
            session(['redirect_after_login' => route('reservation-test.show', $testId)]);

            // Rediriger vers la page de connexion
            return redirect()->route('login')->with('message', 'Veuillez vous connecter pour effectuer une réservation.');
        }

        // Récupérer le test
        $test = TestPsycho::with(['lieu.ville.departement'])->findOrFail($testId);
        
        // Récupérer les types de tests psychotechniques
        $typeTests = TypeTestPsycho::all();

        // Afficher le formulaire de réservation
        return Inertia::render('Front/ReservationTest', [
            'test' => $test,
            'typeTests' => $typeTests,
            'user' => Auth::user(),
        ]);
    }

    public function store(Request $request)
    {
        // Valider les données du formulaire
        $validated = $request->validate([
            'test_psycho_id' => 'required|exists:test_psychos,id',
            'type_test_psycho_id' => 'required|exists:type_test_psychos,id',
            'motif' => 'nullable|string|max:255',
            // Ajouter d'autres validations selon les besoins
        ]);

        // Traiter les fichiers téléchargés si nécessaire
        if ($request->hasFile('permis_recto')) {
            $permisRectoPath = $request->file('permis_recto')->store('documents/permis', 'public');
            $validated['permis_recto'] = $permisRectoPath;
        }

        if ($request->hasFile('permis_verso')) {
            $permisVersoPath = $request->file('permis_verso')->store('documents/permis', 'public');
            $validated['permis_verso'] = $permisVersoPath;
        }

        if ($request->hasFile('document_tribunal')) {
            $documentTribunalPath = $request->file('document_tribunal')->store('documents/tribunal', 'public');
            $validated['document_tribunal'] = $documentTribunalPath;
        }

        // Créer la réservation
        $reservation = Auth::user()->reservationsTestsPsychos()->create([
            'test_psycho_id' => $validated['test_psycho_id'],
            'type_test_psycho_id' => $validated['type_test_psycho_id'],
            'motif' => $validated['motif'] ?? null,
            'permis_recto' => $validated['permis_recto'] ?? null,
            'permis_verso' => $validated['permis_verso'] ?? null,
            'document_tribunal' => $validated['document_tribunal'] ?? null,
            'statut' => 'en attente',
            'date_reservation' => now(),
        ]);

        // Rediriger vers la page de paiement
        return redirect()->route('paiement-test', $reservation->id);
    }
}
