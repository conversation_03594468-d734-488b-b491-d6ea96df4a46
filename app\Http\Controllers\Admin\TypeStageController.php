<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\TypeStage;
use Illuminate\Http\Request;
use Inertia\Inertia;

class TypeStageController extends Controller
{
    public function index()
    {
        return Inertia::render('Admin/TypesStages/Index', [
            'types' => TypeStage::withCount('reservations')->paginate(10)
        ]);
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'nom' => 'required|string|max:255',
            'description' => 'nullable|string'
        ]);

        TypeStage::create($validated);

        return redirect()->back()->with('success', 'Type de stage créé avec succès.');
    }

    public function update(Request $request, TypeStage $typesStage)
    {
        $validated = $request->validate([
            'nom' => 'required|string|max:255',
            'description' => 'nullable|string'
        ]);

        $typesStage->update($validated);

        return redirect()->back()->with('success', 'Type de stage mis à jour avec succès.');
    }

    public function destroy(TypeStage $typesStage)
    {
        $typesStage->delete();
        return redirect()->back()->with('success', 'Type de stage supprimé avec succès.');
    }
}
