<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('reservation_test_psychos', function (Blueprint $table) {
            $table->id();
            $table->foreignId('test_psycho_id')->constrained()->onDelete('cascade');
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('type_test_psycho_id')->constrained('type_test_psychos')->onDelete('cascade');
            $table->timestamp('date_reservation');
            $table->enum('statut', ['confirmée', 'en attente', 'annulée']);
            $table->string('motif')->nullable();
            $table->string('permis_recto')->nullable();
            $table->string('permis_verso')->nullable();
            $table->string('document_tribunal')->nullable();
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('reservation_test_psychos');
    }
};
