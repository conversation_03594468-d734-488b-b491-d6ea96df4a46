import React, { useState, useEffect } from 'react';
import { router } from '@inertiajs/react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Search, Filter, X } from 'lucide-react';
import { Departement, Ville } from '@/types';

interface AdminFiltersProps {
  filters: {
    search?: string;
    departement?: string;
    ville?: string;
    date_debut?: string;
    date_fin?: string;
    prix_min?: string;
    prix_max?: string;
  };
  departements: Departement[];
  villes: Ville[];
  routeName: string;
  isArchive?: boolean;
}

export default function AdminFilters({
  filters,
  departements,
  villes,
  routeName,
  isArchive = false
}: AdminFiltersProps) {
  const [localFilters, setLocalFilters] = useState(filters);
  const [filteredVilles, setFilteredVilles] = useState<Ville[]>([]);
  const [showFilters, setShowFilters] = useState(false);

  // Filter cities based on selected department
  useEffect(() => {
    if (localFilters.departement && localFilters.departement !== 'all') {
      const filtered = villes.filter(ville =>
        ville.departement_id?.toString() === localFilters.departement
      );
      setFilteredVilles(filtered);
    } else {
      setFilteredVilles(villes);
    }
  }, [localFilters.departement, villes]);

  // Check if any filters are active
  const hasActiveFilters = Object.values(filters).some(value => value && value !== '');

  // Show filters by default if any are active
  useEffect(() => {
    if (hasActiveFilters) {
      setShowFilters(true);
    }
  }, [hasActiveFilters]);

  const handleFilterChange = (key: string, value: string) => {
    const newFilters = { ...localFilters, [key]: value };

    // Reset city when department changes
    if (key === 'departement') {
      newFilters.ville = '';
    }

    setLocalFilters(newFilters);
  };

  const applyFilters = () => {
    const cleanFilters = Object.fromEntries(
      Object.entries(localFilters).filter(([_, value]) => value && value !== '')
    );

    console.log('Applying filters:', cleanFilters); // Debug log

    router.get(route(routeName), cleanFilters, {
      preserveState: true,
      preserveScroll: true,
    });
  };

  const clearFilters = () => {
    setLocalFilters({});
    router.get(route(routeName), {}, {
      preserveState: true,
      preserveScroll: true,
    });
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      applyFilters();
    }
  };

  return (
    <Card className="mb-4">
      <CardHeader className="p-4">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Filtres
          </CardTitle>
          <div className="flex items-center gap-2">
            {hasActiveFilters && (
              <Button
                variant="outline"
                size="sm"
                onClick={clearFilters}
                className="text-sm"
              >
                <X className="h-4 w-4 mr-1" />
                Effacer
              </Button>
            )}
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowFilters(!showFilters)}
            >
              {showFilters ? 'Masquer' : 'Afficher'} les filtres
            </Button>
          </div>
        </div>
      </CardHeader>

      {showFilters && (
        <CardContent className="space-y-4">
          {/* Search by reference */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="search">Rechercher par référence</Label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  id="search"
                  placeholder="Référence..."
                  value={localFilters.search || ''}
                  onChange={(e) => handleFilterChange('search', e.target.value)}
                  onKeyPress={handleKeyPress}
                  className="pl-10"
                />
              </div>
            </div>

            {/* Department filter */}
            <div className="space-y-2">
              <Label htmlFor="departement">Département</Label>
              <Select
                value={localFilters.departement || 'all'}
                onValueChange={(value) => handleFilterChange('departement', value === 'all' ? '' : value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Tous les départements" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Tous les départements</SelectItem>
                  {departements.map((dept) => (
                    <SelectItem key={dept.id} value={dept.id.toString()}>
                      {dept.code} - {dept.nom}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* City filter */}
            <div className="space-y-2">
              <Label htmlFor="ville">Ville</Label>
              <Select
                value={localFilters.ville || 'all'}
                onValueChange={(value) => handleFilterChange('ville', value === 'all' ? '' : value)}
                disabled={!localFilters.departement || localFilters.departement === 'all'}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Toutes les villes" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Toutes les villes</SelectItem>
                  {filteredVilles.map((ville) => (
                    <SelectItem key={ville.id} value={ville.id.toString()}>
                      {ville.nom}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Date range and price filters */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="space-y-2">
              <Label htmlFor="date_debut">Date de début</Label>
              <Input
                id="date_debut"
                type="date"
                value={localFilters.date_debut || ''}
                onChange={(e) => handleFilterChange('date_debut', e.target.value)}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="date_fin">Date de fin</Label>
              <Input
                id="date_fin"
                type="date"
                value={localFilters.date_fin || ''}
                onChange={(e) => handleFilterChange('date_fin', e.target.value)}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="prix_min">Prix minimum (€)</Label>
              <Input
                id="prix_min"
                type="number"
                placeholder="0"
                min="0"
                step="0.01"
                value={localFilters.prix_min || ''}
                onChange={(e) => handleFilterChange('prix_min', e.target.value)}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="prix_max">Prix maximum (€)</Label>
              <Input
                id="prix_max"
                type="number"
                placeholder="1000"
                min="0"
                step="0.01"
                value={localFilters.prix_max || ''}
                onChange={(e) => handleFilterChange('prix_max', e.target.value)}
              />
            </div>
          </div>

          {/* Apply filters button */}
          <div className="flex justify-end pt-4">
            <Button onClick={applyFilters} className="min-w-[120px]">
              <Search className="h-4 w-4 mr-2" />
              Appliquer les filtres
            </Button>
          </div>
        </CardContent>
      )}
    </Card>
  );
}
