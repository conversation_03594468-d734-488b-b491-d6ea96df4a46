import { Button } from '@/components/ui/button';
import { Link } from '@inertiajs/react';

interface PaginationLink {
  url: string | null;
  label: string;
  active: boolean;
}

interface PaginationProps {
  links: PaginationLink[];
  from?: number;
  to?: number;
  total?: number;
}

export default function Pagination({ links, from, to, total }: PaginationProps) {
  if (links.length <= 3) return null; // No pagination needed if only 1 page (prev, 1, next)

  return (
    <div className="flex flex-col items-center gap-4 mt-4">
      <div className="flex items-center gap-2">
        {links.map((link, index) => {
          // Clean up the pagination labels
          const label = link.label
            .replace('Previous', '')
            .replace('Next', '')
            .replace(/^&laquo;\s*/, '«')
            .replace(/&raquo;$/, '»');

          if (!link.url) {
            return (
              <Button
                key={index}
                variant="outline"
                size="icon"
                disabled
                className="h-8 w-8"
              >
                <span>{label}</span>
              </Button>
            );
          }

          return (
            <Button
              key={index}
              variant={link.active ? "default" : "outline"}
              size="icon"
              className="h-8 w-8"
              asChild
            >
              <Link
                href={link.url}
                preserveScroll
                preserveState
              >
                <span>{label}</span>
              </Link>
            </Button>
          );
        })}
      </div>
      {from && to && total && (
        <div className="text-sm text-muted-foreground">
          Affichage des éléments {from} à {to} sur {total}
        </div>
      )}
    </div>
  );
}
