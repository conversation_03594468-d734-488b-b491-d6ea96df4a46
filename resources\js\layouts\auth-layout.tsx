import AuthLayoutTemplate from '@/layouts/auth/auth-simple-layout';
import FrontLayout from './front-layout';

export default function AuthLayout({
  children,
  title,
  description,
  width,
  ...props
}: {
  children: React.ReactNode;
  title: string;
  description: string;
  width?: string;
}) {
  return (
    <FrontLayout title={title}>
      <AuthLayoutTemplate title={title} description={description} width={width} {...props}>
        {children}
      </AuthLayoutTemplate>
    </FrontLayout>
  );
}
