<?php

namespace Database\Seeders;

use App\Models\Reservation;
use App\Models\Stage;
use App\Models\TypeStage;
use App\Models\User;
use Illuminate\Database\Seeder;

class ReservationSeeder extends Seeder
{
    public function run(): void
    {
        // Get existing data
        $stages = Stage::all();
        $users = User::where('role', 'client')->get();
        $typeStages = TypeStage::all();

        // Skip if any of these are empty
        if ($stages->isEmpty() || $users->isEmpty() || $typeStages->isEmpty()) {
            return;
        }

        // Create between 2 - 5 reservations each day for the last 80 days

        for ($i = 0; $i < 80; $i++) {
            $reservationsCount = rand(2, 5);

            for ($j = 0; $j < $reservationsCount; $j++) {
                Reservation::create([
                    'stage_id' => $stages->random()->id,
                    'user_id' => $users->random()->id,
                    'type_stage_id' => $typeStages->random()->id,
                    'date_reservation' => now()->subDays($i),
                    'statut' => ['confirmée', 'en attente', 'annulée'][rand(0, 2)],
                    'date_infraction' => now()->subDays(rand(1, 60)),
                    'heure_infraction' => now()->subDays(rand(1, 60))->format('H:i'),
                    'lieu_infraction' => 'Lieu d\'infraction ' . $i,
                    'permis_recto' => 'permis/recto/sample_' . $i . '.pdf',
                    'permis_verso' => 'permis/verso/sample_' . $i . '.pdf',
                    'lettre_48n_recto' => 'lettre48n/recto/sample_' . $i . '.pdf',
                    'lettre_48n_verso' => 'lettre48n/verso/sample_' . $i . '.pdf',
                    'cas' => 'Cas ' . $i,
                    'date_infr' => now()->subDays(rand(1, 60)),
                    'heure_infr' => now()->subDays(rand(1, 60))->format('H:i'),
                    'lieu_infr' => 'Lieu d\'infraction ' . $i,
                    'date_paiement' => now()->subDays(rand(1, 60)),
                    'methode_paiement' => ['paypal', 'virement', 'cheque', 'bon'][rand(0, 3)],
                    'transaction_id' => 'transaction_' . $i,
                ]);
            }
        }
    }
}
