<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;

class UserSeeder extends Seeder
{
    public function run(): void
    {
        // Créer un admin par défaut
        User::factory()->create([
            'nom' => 'Admin',
            'prenom' => 'System',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'role' => 'admin',
        ]);

        // Créer quelques utilisateurs de test
        User::factory(10)->create([
            'role' => 'client',
        ]);
    }
}
