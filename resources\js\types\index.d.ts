import { LucideIcon } from 'lucide-react';
import type { Config } from 'ziggy-js';

export interface Auth {
    user: User;
}

export interface BreadcrumbItem {
    title: string;
    href: string;
}

export interface NavGroup {
    title: string;
    items: NavItem[];
}

export interface NavItem {
    type?: string | null;
    title: string;
    href?: string;
    icon?: LucideIcon | null;
    isActive?: boolean;
    children?: NavItem[];
}

export interface SharedData {
    name: string;
    quote: { message: string; author: string };
    auth: Auth;
    ziggy: Config & { location: string };
    [key: string]: unknown;
}

export interface User {
    id: number;
    nom: string;
    prenom: string;
    email: string;
    avatar?: string;
    email_verified_at: string | null;
    created_at: string;
    updated_at: string;
    role: 'admin' | 'client';
    [key: string]: unknown; // This allows for additional properties...
}

export interface Departement {
    id: number;
    nom: string;
    code: string;
    villes: Ville[];
}

export interface Ville {
    id: number;
    nom: string;
    code?: string;
    reg_code?: string;
    dep_code?: string;
    code_postal?: string;
    departement_id: number;
    departement?: Departement;
    lieus: Lieu[];
}

export interface Lieu {
    id: number;
    nom: string;
    adresse: string;
    ville_id: number;
    ville?: {
        id: number;
        nom: string;
        departement?: {
            id: number;
            nom: string;
            code: string;
        };
    };
    stages?: {
        id: number;
        date_debut: string;
        date_fin: string;
        places_disponibles: number;
        prix: number;
        reference: string;
    }[];
    testPsychos?: TestPsycho[];
}

export interface Stage {
    id: number;
    date_debut: string;
    date_fin: string;
    lieu_id: number;
    places_disponibles: number;
    prix: number;
    reference: string;
    lieu?: Lieu;
    reservations: Reservation[];
}

export interface Reservation {
    id: number;
    stage_id: number;
    user_id: number;
    type_stage_id: number;
    date_reservation: string;
    statut: 'confirmée' | 'en attente' | 'annulée';
    date_infraction?: string;
    heure_infraction?: string;
    lieu_infraction?: string;
    permis_recto?: string;
    permis_verso?: string;
    lettre_48n_recto?: string;
    lettre_48n_verso?: string;
    stage?: Stage;
    user?: User;
    typeStage?: TypeStage;
}

export interface TypeStage {
    id: number;
    nom: string;
    description: string | null;
    reservations_count?: number;
    reservations?: Reservation[];
}

export interface TestPsycho {
    id: number;
    date: string;
    heure: string;
    lieu_id: number;
    places_disponibles: number;
    prix: number;
    reference: string;
    lieu?: Lieu;
    reservations_count?: number;
    reservations?: ReservationTestPsycho[];
}

export interface TypeTestPsycho {
    id: number;
    nom: string;
    description: string | null;
    reservations_count?: number;
    reservations?: ReservationTestPsycho[];
}

export interface ReservationTestPsycho {
    id: number;
    test_psycho_id: number;
    user_id: number;
    type_test_psycho_id: number;
    date_reservation: string;
    statut: 'confirmée' | 'en attente' | 'annulée';
    motif?: string;
    permis_recto?: string;
    permis_verso?: string;
    document_tribunal?: string;
    test_psycho?: TestPsycho;
    user?: User;
    type_test_psycho?: TypeTestPsycho;
}

export type PageProps<T extends Record<string, unknown> = Record<string, unknown>> = T & {
    auth: {
        user: User;
    };
};



