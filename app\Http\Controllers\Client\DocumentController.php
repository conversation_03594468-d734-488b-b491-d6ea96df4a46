<?php

namespace App\Http\Controllers\Client;

use App\Http\Controllers\Controller;
use App\Models\Reservation;
use App\Models\ReservationTestPsycho;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;

class DocumentController extends Controller
{
    public function index()
    {
        // Récupérer les réservations de stages avec documents
        $stageReservations = Reservation::with(['stage.lieu.ville', 'typeStage'])
            ->where('user_id', Auth::id())
            ->where(function ($query) {
                $query->whereNotNull('permis_recto')
                    ->orWhereNotNull('permis_verso')
                    ->orWhereNotNull('lettre_48n_recto')
                    ->orWhereNotNull('lettre_48n_verso');
            })
            ->orderBy('date_reservation', 'desc')
            ->get();

        // Récupérer les réservations de tests psychotechniques avec documents
        $testReservations = ReservationTestPsycho::with(['testPsycho.lieu.ville', 'typeTestPsycho'])
            ->where('user_id', Auth::id())
            ->where(function ($query) {
                $query->whereNotNull('permis_recto')
                    ->orWhereNotNull('permis_verso')
                    ->orWhereNotNull('document_tribunal');
            })
            ->orderBy('date_reservation', 'desc')
            ->get();

        return Inertia::render('Client/Documents', [
            'stageReservations' => $stageReservations,
            'testReservations' => $testReservations
        ]);
    }
}
