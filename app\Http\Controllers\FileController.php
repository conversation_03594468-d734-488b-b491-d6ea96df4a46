<?php

namespace App\Http\Controllers;

use App\Models\Reservation;
use App\Models\ReservationTestPsycho;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;

class FileController extends Controller
{
    /**
     * Télécharger un fichier avec vérification des autorisations
     *
     * @param Request $request
     * @param string $path Chemin du fichier dans le stockage
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse|\Illuminate\Http\RedirectResponse
     */
    public function download(Request $request, $path)
    {
        // Vérifier si l'utilisateur est authentifié
        if (!Auth::check()) {
            abort(403, 'Accès non autorisé. Veuillez vous connecter.');
        }

        $user = Auth::user();
        // $fullPath = 'app/public/' . $path;
        $fullPath = $path;

        // Vérifier si le fichier existe
        if (!Storage::exists($fullPath)) {
            abort(404, 'Fichier non trouvé.');
        }

        // Si l'utilisateur est un administrateur, autoriser l'accès sans restriction
        if ($user->role === 'admin') {
            return Storage::download($fullPath);
        }

        // Pour les utilisateurs normaux (clients), vérifier si le fichier leur appartient
        if ($user->role === 'client') {
            // Vérifier si le fichier est lié à une réservation de stage de l'utilisateur
            $stageReservation = Reservation::where('user_id', $user->id)
                ->where(function ($query) use ($path) {
                    $query->where('permis_recto', $path)
                        ->orWhere('permis_verso', $path)
                        ->orWhere('lettre_48n_recto', $path)
                        ->orWhere('lettre_48n_verso', $path);
                })
                ->first();

            if ($stageReservation) {
                return Storage::download($fullPath);
            }

            // Vérifier si le fichier est lié à une réservation de test psychotechnique de l'utilisateur
            $testReservation = ReservationTestPsycho::where('user_id', $user->id)
                ->where(function ($query) use ($path) {
                    $query->where('permis_recto', $path)
                        ->orWhere('permis_verso', $path)
                        ->orWhere('document_tribunal', $path);
                })
                ->first();

            if ($testReservation) {
                return Storage::download($fullPath);
            }

            // Si le fichier n'est pas lié à une réservation de l'utilisateur, refuser l'accès
            abort(403, 'Vous n\'êtes pas autorisé à accéder à ce fichier.');
        }

        // Par défaut, refuser l'accès
        abort(403, 'Accès non autorisé.');
    }
}
