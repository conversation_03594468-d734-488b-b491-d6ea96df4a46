"use client";

import { useEffect, useState } from "react";
import {
  Carousel,
  CarouselApi,
  CarouselContent,
  CarouselItem,
} from "@/components/ui/carousel";
import { Quote } from "lucide-react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Card, CardContent } from "@/components/ui/card";

interface Testimonial {
  name: string;
  role: string;
  content: string;
  avatar?: string;
}

const testimonials: Testimonial[] = [
  {
    name: "<PERSON>",
    role: "Conductrice",
    content: "J'ai pu récupérer mes points de permis grâce à un stage très bien organisé. Les formateurs étaient compétents et pédagogues. Je recommande vivement !",
    avatar: "https://images.unsplash.com/photo-1494790108377-be9c29b29330?ixlib=rb-1.2.1&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80"
  },
  {
    name: "<PERSON>",
    role: "Chauffeur professionnel",
    content: "En tant que professionnel de la route, j'avais besoin de récupérer mes points rapidement. Le stage était parfaitement adapté à mes besoins et m'a permis de reprendre la route en toute légalité.",
    avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-1.2.1&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80"
  },
  {
    name: "Sophie Leroy",
    role: "Étudiante",
    content: "J'ai passé le test psychotechnique pour récupérer mon permis. L'équipe a été très professionnelle et m'a mise à l'aise tout au long du processus. Merci !",
    avatar: "https://images.unsplash.com/photo-1534528741775-53994a69daeb?ixlib=rb-1.2.1&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80"
  },
  {
    name: "Jean Moreau",
    role: "Retraité",
    content: "À 65 ans, j'avais peur de ne pas réussir le stage, mais les formateurs ont été très patients et ont adapté leur pédagogie. Une expérience très enrichissante.",
    avatar: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-1.2.1&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80"
  },
  {
    name: "Camille Bernard",
    role: "Commerciale",
    content: "Le stage était non seulement utile pour récupérer mes points, mais aussi très instructif. J'ai appris beaucoup sur la sécurité routière et cela a changé ma façon de conduire.",
    avatar: "https://images.unsplash.com/photo-1487412720507-e7ab37603c6f?ixlib=rb-1.2.1&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80"
  }
];

export function TestimonialsFr() {
  const [api, setApi] = useState<CarouselApi>();
  const [current, setCurrent] = useState(0);

  useEffect(() => {
    if (!api) {
      return;
    }

    const interval = setInterval(() => {
      if (api.selectedScrollSnap() + 1 === api.scrollSnapList().length) {
        setCurrent(0);
        api.scrollTo(0);
      } else {
        api.scrollNext();
        setCurrent(current + 1);
      }
    }, 5000);

    return () => clearInterval(interval);
  }, [api, current]);

  return (
    <div className="w-full py-12">
      <div className="container mx-auto">
        <div className="flex flex-col gap-8">
          <Carousel setApi={setApi} className="w-full">
            <CarouselContent>
              {testimonials.map((testimonial, index) => (
                <CarouselItem key={index} className="md:basis-1/2 lg:basis-1/3">
                  <Card className="h-full">
                    <CardContent className="p-6 flex flex-col gap-4 h-full">
                      <Quote className="h-8 w-8 text-primary/40" />
                      <p className="text-sm flex-grow">{testimonial.content}</p>
                      <div className="flex items-center gap-3 mt-4">
                        <Avatar className="h-10 w-10">
                          <AvatarImage src={testimonial.avatar} alt={testimonial.name} />
                          <AvatarFallback>{testimonial.name.charAt(0)}</AvatarFallback>
                        </Avatar>
                        <div>
                          <p className="font-medium text-sm">{testimonial.name}</p>
                          <p className="text-xs text-muted-foreground">{testimonial.role}</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </CarouselItem>
              ))}
            </CarouselContent>
          </Carousel>
        </div>
      </div>
    </div>
  );
}
