<?php

namespace Database\Seeders;

use App\Models\Configuration;
use Illuminate\Database\Seeder;

class ConfigurationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Payment method configurations
        $paymentMethods = [
            [
                'key' => 'payment_method_paypal_enabled',
                'value' => '1',
                'type' => 'boolean',
                'description' => 'Enable/disable PayPal payment method',
                'group' => 'payment_methods',
            ],
            [
                'key' => 'payment_method_sumup_enabled',
                'value' => '1',
                'type' => 'boolean',
                'description' => 'Enable/disable SumUp payment method',
                'group' => 'payment_methods',
            ],
            [
                'key' => 'payment_method_virement_enabled',
                'value' => '1',
                'type' => 'boolean',
                'description' => 'Enable/disable bank transfer payment method',
                'group' => 'payment_methods',
            ],
            [
                'key' => 'payment_method_cheque_enabled',
                'value' => '1',
                'type' => 'boolean',
                'description' => 'Enable/disable check payment method',
                'group' => 'payment_methods',
            ],
            [
                'key' => 'payment_method_bon_enabled',
                'value' => '1',
                'type' => 'boolean',
                'description' => 'Enable/disable voucher payment method',
                'group' => 'payment_methods',
            ],
            [
                'key' => 'payment_method_paiement_sur_place_enabled',
                'value' => '1',
                'type' => 'boolean',
                'description' => 'Enable/disable on-site payment method',
                'group' => 'payment_methods',
            ],
        ];

        foreach ($paymentMethods as $config) {
            Configuration::updateOrCreate(
                ['key' => $config['key']],
                $config
            );
        }

        // General application configurations
        $generalConfigs = [
            [
                'key' => 'app_maintenance_mode',
                'value' => '0',
                'type' => 'boolean',
                'description' => 'Enable/disable application maintenance mode',
                'group' => 'general',
            ],
            [
                'key' => 'max_reservations_per_user',
                'value' => '10',
                'type' => 'integer',
                'description' => 'Maximum number of reservations per user',
                'group' => 'general',
            ],
        ];

        foreach ($generalConfigs as $config) {
            Configuration::updateOrCreate(
                ['key' => $config['key']],
                $config
            );
        }
    }
}
