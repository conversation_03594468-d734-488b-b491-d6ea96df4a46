<?php

namespace Database\Factories;

use App\Models\TestPsycho;
use App\Models\Lieu;
use Illuminate\Database\Eloquent\Factories\Factory;

class TestPsychoFactory extends Factory
{
    protected $model = TestPsycho::class;

    public function definition(): array
    {
        $date = fake()->dateTimeBetween('+1 week', '+3 months');

        return [
            'date' => $date,
            'lieu_id' => Lieu::factory(),
            'places_disponibles' => fake()->numberBetween(5, 20),
            'prix' => fake()->randomFloat(2, 100, 300),
            'reference' => 'TP-' . strtoupper(fake()->unique()->bothify('??###')),
        ];
    }
}
