<?php

namespace App\Notifications;

use App\Models\ReservationTestPsycho;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class TestReservationConfirmedNotification extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * The test reservation instance.
     *
     * @var \App\Models\ReservationTestPsycho
     */
    protected $reservation;

    /**
     * Create a new notification instance.
     */
    public function __construct(ReservationTestPsycho $reservation)
    {
        $this->reservation = $reservation;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $reservation = $this->reservation;
        $user = $reservation->user;
        $testPsycho = $reservation->testPsycho;
        $typeTest = $reservation->typeTestPsycho;

        return (new MailMessage)
            ->subject('Nouvelle réservation confirmée - Test psychotechnique #' . $reservation->id)
            ->view('emails.test-reservation-confirmed', [
                'reservation' => $reservation,
                'user' => $user,
                'testPsycho' => $testPsycho,
                'typeTest' => $typeTest,
            ]);
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'reservation_id' => $this->reservation->id,
            'user_name' => $this->reservation->user->nom . ' ' . $this->reservation->user->prenom,
            'user_email' => $this->reservation->user->email,
            'test_date' => $this->reservation->testPsycho->date->format('Y-m-d'),
            'type_test' => $this->reservation->typeTestPsycho->nom ?? null,
            'statut' => $this->reservation->statut,
            'methode_paiement' => $this->reservation->methode_paiement,
        ];
    }
}
