import { Head } from '@inertiajs/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import ClientLayout from '@/layouts/client-layout';
import { ReservationTestPsycho } from '@/types';
import { format } from 'date-fns';
import { fr } from 'date-fns/locale';
import { Badge } from '@/components/ui/badge';
import { CalendarDays, MapPin, FileText } from 'lucide-react';

interface TestsProps {
  reservations: ReservationTestPsycho[];
}

export default function Tests({ reservations = [] }: TestsProps) {
  // Filtrer uniquement les réservations confirmées
  const confirmedReservations = reservations.filter(r => r.statut === 'confirmée');

  return (
    <ClientLayout title="Mes tests psychotechniques">
      <Head title="Mes tests psychotechniques" />

      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-bold tracking-tight">Mes tests psychotechniques</h1>
        </div>

        {confirmedReservations.length === 0 ? (
          <Card>
            <CardContent className="py-10 text-center">
              <p className="text-muted-foreground">Vous n'avez pas encore de tests psychotechniques confirmés.</p>
            </CardContent>
          </Card>
        ) : (
          <div className="space-y-4">
            {confirmedReservations.map((reservation) => {
              const test = reservation.test_psycho;
              const date = new Date(test.date);
              const isUpcoming = date > new Date();

              return (
                <Card key={reservation.id} className={isUpcoming ? '' : 'opacity-70'}>
                  <CardHeader className="pb-2">
                    <div className="flex items-center justify-between">
                      <CardTitle>
                        Test du {format(date, 'dd MMMM yyyy', { locale: fr })}
                      </CardTitle>
                      {isUpcoming ? (
                        <Badge className="bg-green-500">À venir</Badge>
                      ) : (
                        <Badge variant="outline">Terminé</Badge>
                      )}
                    </div>
                    <CardDescription>
                      {reservation.type_test_psycho.nom}
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="grid gap-4">
                      <div className="flex items-center gap-2">
                        <CalendarDays className="h-4 w-4 text-muted-foreground" />
                        <span>
                          {format(date, 'dd/MM/yyyy à HH:mm', { locale: fr })}
                        </span>
                      </div>
                      <div className="flex items-center gap-2">
                        <MapPin className="h-4 w-4 text-muted-foreground" />
                        <span>
                          {test.lieu.nom} - {test.lieu.adresse}, {test.lieu.ville.nom}
                        </span>
                      </div>
                      {reservation.motif && (
                        <div className="flex items-center gap-2">
                          <FileText className="h-4 w-4 text-muted-foreground" />
                          <span>
                            Motif : {reservation.motif}
                          </span>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        )}
      </div>
    </ClientLayout>
  );
}
