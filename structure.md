# Plan de travail pour la base de données et le stack Laravel + Inertia + ReactJS

## Ajout des tables

### Table: type_stages

- **id** (clé primaire)
- **nom** (string, ex: "Récupération volontaire de 4 points", "Stage en période probatoire", etc.)
- **description** (text, optionnel)

### Table: reservations (Mise à jour)

- **id** (clé primaire)
- **stage_id** (clé étrangère vers `stages.id`)
- **user_id** (clé étrangère vers `users.id`)
- **type_stage_id** (clé étrangère vers `type_stages.id`)
- **date_reservation** (timestamp)
- **statut** (enum: 'confirmée', 'en attente', 'annulée')
- **date_infraction** (date, nullable)
- **heure_infraction** (time, nullable)
- **lieu_infraction** (string, nullable)
- **permis_recto** (string, chemin vers le fichier, nullable)
- **permis_verso** (string, chemin vers le fichier, nullable)
- **lettre_48n_recto** (string, chemin vers le fichier, nullable)
- **lettre_48n_verso** (string, chemin vers le fichier, nullable)

### Table: departements

- **id** (clé primaire)
- **nom** (string)
- **code** (string, ex: "27" pour l'Eure)

### Table: villes

- **id** (clé primaire)
- **nom** (string)
- **departement_id** (clé étrangère vers `departements.id`)

### Table: lieux

- **id** (clé primaire)
- **nom** (string, ex: "Centre de formation X")
- **adresse** (string)
- **ville_id** (clé étrangère vers `villes.id`)

### Table: stages

- **id** (clé primaire)
- **date_debut** (date)
- **date_fin** (date)
- **lieu_id** (clé étrangère vers `lieux.id`)
- **places_disponibles** (integer)
- **prix** (decimal)
- **reference** (string)

### Table: users

- **id** (clé primaire)
- **nom** (string)
- **prenom** (string)
- **email** (string)
- **password** (string)
- **role** (enum: 'admin', 'client')

---

## Relations entre les tables (Mise à jour)

1. Un département (**departements**) peut avoir plusieurs villes (**villes**) :
   - Relation *one-to-many* entre `departements` et `villes`.

2. Une ville (**villes**) peut avoir plusieurs lieux (**lieux**) :
   - Relation *one-to-many* entre `villes` et `lieux`.

3. Un lieu (**lieux**) peut accueillir plusieurs stages (**stages**) :
   - Relation *one-to-many* entre `lieux` et `stages`.

4. Un stage (**stages**) peut avoir plusieurs réservations (**reservations**) :
   - Relation *one-to-many* entre `stages` et `reservations`.

5. Un utilisateur (**users**) peut effectuer plusieurs réservations (**reservations**) :
   - Relation *one-to-many* entre `users` et `reservations`.

6. Un type de stage (**type_stages**) peut être lié à plusieurs réservations (**reservations**) :
    - Relation *one-to-many* entre `type_stages` et `reservations`.
