import FrontLayout from '@/layouts/front-layout';
import { Card, CardContent } from '@/components/ui/card';
import { HeroSection } from '@/components/hero-section-dark';
import { Button } from '@/components/ui/button';
import { Link } from '@inertiajs/react';
import { Scale, MessageSquareText } from 'lucide-react';

export default function AppuiAvocat() {
  return (
    <FrontLayout title="Permis en danger - L'appui d'un avocat">
      <HeroSection
        subtitle={{
          regular: "PERMIS EN DANGER : ",
          gradient: "L'appui d'un avocat spécialisé"
        }}
        className="mb-8"
        gridOptions={{
          angle: 65,
          cellSize: 60,
          opacity: 0.3,
          lightLineColor: "#b60062",
          darkLineColor: "#b60062"
        }}
      >
        <div className="flex flex-wrap gap-4 mt-6 justify-center">
          <Button size="lg" asChild>
            <Link href={route('contact')}>
              Consultation gratuite
            </Link>
          </Button>
          <Button variant="outline" size="lg" asChild>
            <Link href={route('permis-en-danger')}>
              En savoir plus
            </Link>
          </Button>
        </div>
      </HeroSection>

      <div className="container mx-auto px-4 py-8">
        <div className="grid gap-8 md:grid-cols-2 mb-8">
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center gap-3 mb-4">
                <Scale className="h-8 w-8 text-primary" />
                <h2 className="text-xl font-semibold">Notre expertise juridique</h2>
              </div>
              <p className="text-muted-foreground mb-4">
                STAGE PERMIS travaille en partenariat avec un Cabinet d'avocats qui dispose d'un département spécialisé en Droit Routier.
              </p>
              <p className="text-muted-foreground mb-4">
                Lorsque vous cliquez sur le lien "Permis en danger" pour poser une question, STAGE PERMIS{' '}
                <span className="font-semibold text-foreground">
                  vous adresse sa propre réponse, fondée sur notre expérience et sur notre connaissance du système du permis à points
                </span>.
              </p>
              <p className="text-muted-foreground">
                En même temps que nous vous communiquons notre réponse à votre question, nous la communiquons – en protégeant bien sûr de façon stricte votre anonymat – également à notre Cabinet d'avocats lequel dispose d'un département spécialisé en Droit Routier.
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center gap-3 mb-4">
                <MessageSquareText className="h-8 w-8 text-primary" />
                <h2 className="text-xl font-semibold">Notre engagement</h2>
              </div>
              <p className="text-muted-foreground mb-4">
                Nous serons ainsi en mesure de vous communiquer un premier avis du Cabinet sur la possibilité et la pertinence d'un recours sous un délai de 5 jours.
              </p>
              <p className="text-muted-foreground mb-4">
                Nos Avocats mettent au service de STAGE PERMIS une expertise et un savoir-faire en droit pénal routier et droit administratif afin d'assurer la défense de vos intérêts de conducteur et la sauvegarde de votre permis de conduire.
              </p>
            </CardContent>
          </Card>
        </div>

        <Card className="mb-8">
          <CardContent className="pt-6">
            <h2 className="text-xl font-semibold mb-4">Comment pouvons-nous vous aider ?</h2>
            <p className="mb-6">
              <span className="font-semibold">Faire appel à un Avocat spécialisé peut vous permettre de récupérer un permis de conduire annulé</span>{' '}
              mais également d'organiser une défense suite à une infraction (alcool au volant, excès de vitesse...), de contester valablement des avis de contravention ou, à défaut, de retarder le retrait de points.
            </p>
            <p className="text-muted-foreground mb-6">
              Ainsi, vous bénéficierez de conseils en temps réels, adaptés et personnalisés. L'analyse de votre situation par un cabinet expert en la matière permettra de vous fixer sur les chances de succès d'une procédure.
            </p>
            <div className="flex justify-center mt-8">
              <Button size="lg" className="bg-primary hover:bg-primary/90" asChild>
                <Link href={route('contact')}>
                  INTERROGEZ-NOUS MAINTENANT !
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </FrontLayout>
  );
}
