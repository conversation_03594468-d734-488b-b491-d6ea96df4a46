<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Inertia\Inertia;

class ClientController extends Controller
{
    /**
     * Affiche la liste des clients.
     *
     * @return \Inertia\Response
     */
    public function index()
    {
        return Inertia::render('Admin/Clients/Index', [
            'clients' => User::where('role', 'client')
                ->with(['reservations.stage.lieu.ville.departement', 'reservations.typeStage', 'reservationsTestsPsychos.testPsycho.lieu.ville.departement', 'reservationsTestsPsychos.typeTestPsycho'])
                ->withCount(['reservations', 'reservationsTestsPsychos'])
                ->paginate(10)
        ]);
    }

    /**
     * Enregistre un nouveau client.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'civilite' => 'nullable|string|in:M.,Mme',
            'nom' => 'required|string|max:255',
            'prenom' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:8',
            'date_naissance' => 'nullable|date',
            'lieu_naissance' => 'nullable|string|max:255',
            'ville' => 'nullable|string|max:255',
            'code_postal' => 'nullable|string|max:10',
            'adresse' => 'nullable|string|max:255',
            'mobile' => 'nullable|string|max:20',
            'tel' => 'nullable|string|max:20',
            'num_permis' => 'nullable|string|max:255',
            'date_permis' => 'nullable|date',
            'lieu_permis' => 'nullable|string|max:255',
        ]);

        // Définir le rôle comme client
        $validated['role'] = 'client';

        // Hasher le mot de passe
        $validated['password'] = bcrypt($validated['password']);

        User::create($validated);

        return redirect()->back()->with('success', 'Client créé avec succès.');
    }

    /**
     * Met à jour un client existant.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\User  $client
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(Request $request, User $client)
    {
        // Vérifier que l'utilisateur est bien un client
        if ($client->role !== 'client') {
            return redirect()->back()->with('error', 'Cet utilisateur n\'est pas un client.');
        }

        $validated = $request->validate([
            'civilite' => 'nullable|string|in:M.,Mme',
            'nom' => 'required|string|max:255',
            'prenom' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users,email,' . $client->id,
            'date_naissance' => 'nullable|date',
            'lieu_naissance' => 'nullable|string|max:255',
            'ville' => 'nullable|string|max:255',
            'code_postal' => 'nullable|string|max:10',
            'adresse' => 'nullable|string|max:255',
            'mobile' => 'nullable|string|max:20',
            'tel' => 'nullable|string|max:20',
            'num_permis' => 'nullable|string|max:255',
            'date_permis' => 'nullable|date',
            'lieu_permis' => 'nullable|string|max:255',
        ]);

        // Mettre à jour le mot de passe si fourni
        if ($request->filled('password')) {
            $request->validate([
                'password' => 'string|min:8',
            ]);
            $validated['password'] = bcrypt($request->password);
        }

        $client->update($validated);

        return redirect()->back()->with('success', 'Client mis à jour avec succès.');
    }

    /**
     * Supprime un client.
     *
     * @param  \App\Models\User  $client
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy(User $client)
    {
        // Vérifier que l'utilisateur est bien un client
        if ($client->role !== 'client') {
            return redirect()->back()->with('error', 'Cet utilisateur n\'est pas un client.');
        }

        // Vérifier si le client a des réservations
        if ($client->reservations()->count() > 0 || $client->reservationsTestsPsychos()->count() > 0) {
            return redirect()->back()->with('error', 'Ce client a des réservations et ne peut pas être supprimé.');
        }

        $client->delete();

        return redirect()->back()->with('success', 'Client supprimé avec succès.');
    }

    /**
     * Affiche les détails d'un client spécifique.
     *
     * @param  \App\Models\User  $client
     * @return \Inertia\Response
     */
    public function show(User $client)
    {
        // Vérifier que l'utilisateur est bien un client
        if ($client->role !== 'client') {
            return redirect()->route('admin.clients.index')->with('error', 'Cet utilisateur n\'est pas un client.');
        }

        return Inertia::render('Admin/Clients/Show', [
            'client' => $client->load([
                'reservations.stage.lieu.ville.departement',
                'reservations.typeStage',
                'reservationsTestsPsychos.testPsycho.lieu.ville.departement',
                'reservationsTestsPsychos.typeTestPsycho'
            ])
        ]);
    }
}
