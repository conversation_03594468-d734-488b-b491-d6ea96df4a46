"use client";
import React from "react";
import { motion } from "motion/react";

// Browser detection hook
const useBrowserDetection = () => {
  const [isSafari, setIsSafari] = React.useState(false);

  React.useEffect(() => {
    if (typeof window !== 'undefined') {
      try {
        const userAgent = window.navigator.userAgent.toLowerCase();
        const safariDetected = (
          userAgent.includes('safari') &&
          !userAgent.includes('chrome') &&
          !userAgent.includes('chromium') &&
          !userAgent.includes('edge') &&
          !userAgent.includes('firefox')
        ) || (
          userAgent.includes('mobile') && userAgent.includes('safari') && !userAgent.includes('chrome')
        ) || (
          /version\/[\d.]+.*safari/i.test(userAgent) && !/chrome|chromium|edge|firefox/i.test(userAgent)
        );

        setIsSafari(safariDetected);
      } catch (error) {
        setIsSafari(false);
      }
    }
  }, []);

  return { isSafari };
};

export function ColourfulText({ text }: { text: string }) {
  const { isSafari } = useBrowserDetection();
  const colors = [
    "rgb(182 0 98)",
    // "rgb(131, 179, 32)",
    // "rgb(47, 195, 106)",
    // "rgb(42, 169, 210)",
    // "rgb(4, 112, 202)",
    // "rgb(107, 10, 255)",
    // "rgb(183, 0, 218)",
    // "rgb(218, 0, 171)",
    // "rgb(230, 64, 92)",
    // "rgb(232, 98, 63)",
    // "rgb(249, 129, 47)",
  ];

  const [currentColors, setCurrentColors] = React.useState(colors);
  const [count, setCount] = React.useState(0);

  React.useEffect(() => {
    // Only run animations if not Safari
    if (!isSafari) {
      const interval = setInterval(() => {
        const shuffled = [...colors].sort(() => Math.random() - 0.5);
        setCurrentColors(shuffled);
        setCount((prev) => prev + 1);
      }, 7000);

      return () => clearInterval(interval);
    }
  }, [isSafari]);

  // Safari-friendly version without complex animations
  if (isSafari) {
    return (
      <span className="font-heading inline-block whitespace-pre tracking-tight text-primary">
        {text}
      </span>
    );
  }

  // Full-featured version for other browsers
  return text.split("").map((char, index) => (
    <motion.span
      key={`${char}-${count}-${index}`}
      initial={{
        y: 0,
      }}
      animate={{
        color: currentColors[index % currentColors.length],
        y: [0, -3, 0],
        scale: [1, 1.01, 1],
        // Remove blur filter for better Safari compatibility
        opacity: [1, 0.8, 1],
      }}
      transition={{
        duration: 0.8,
        delay: index * 0.05,
      }}
      className="font-heading inline-block whitespace-pre tracking-tight"
    >
      {char}
    </motion.span>
  ));
}
