<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('reservation_test_psychos', function (Blueprint $table) {
            $table->timestamp('date_paiement')->nullable()->after('document_tribunal');
            $table->string('methode_paiement')->nullable()->after('date_paiement');
            $table->string('transaction_id')->nullable()->after('methode_paiement');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('reservation_test_psychos', function (Blueprint $table) {
            $table->dropColumn('date_paiement');
            $table->dropColumn('methode_paiement');
            $table->dropColumn('transaction_id');
        });
    }
};
