<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Email Security Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains security-related email configuration options
    | for production environments.
    |
    */

    // Use environment variables for all sensitive data
    'smtp' => [
        'host' => env('MAIL_HOST'),
        'port' => env('MAIL_PORT', 587),
        'username' => env('MAIL_USERNAME'),
        'password' => env('MAIL_PASSWORD'),
        'encryption' => env('MAIL_ENCRYPTION', 'tls'),
    ],

    // Security headers for emails
    'security_headers' => [
        'X-Mailer' => 'Laravel',
        'X-Priority' => '3',
        'X-MSMail-Priority' => 'Normal',
    ],

    // Rate limiting for email sending
    'rate_limiting' => [
        'enabled' => env('MAIL_RATE_LIMITING', true),
        'max_emails_per_minute' => env('MAIL_MAX_PER_MINUTE', 60),
        'max_emails_per_hour' => env('MAIL_MAX_PER_HOUR', 1000),
    ],

    // Email validation settings
    'validation' => [
        'verify_ssl' => env('MAIL_VERIFY_SSL', true),
        'timeout' => env('MAIL_TIMEOUT', 30),
    ],
];
