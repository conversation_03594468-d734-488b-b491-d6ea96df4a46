import { Head, useForm } from '@inertiajs/react';
import { LoaderCircle } from 'lucide-react';
import { FormEventHandler } from 'react';

import InputError from '@/components/input-error';
import TextLink from '@/components/text-link';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import AuthLayout from '@/layouts/auth-layout';

type RegisterForm = {
  civilite: string;
  nom: string;
  prenom: string;
  email: string;
  password: string;
  password_confirmation: string;
  date_naissance: string;
  lieu_naissance: string;
  ville: string;
  code_postal: string;
  adresse: string;
  mobile: string;
  tel: string;
  num_permis: string;
  date_permis: string;
  lieu_permis: string;
};

export default function Register() {
  const { data, setData, post, processing, errors, reset } = useForm<Required<RegisterForm>>({
    civilite: '',
    nom: '',
    prenom: '',
    email: '',
    password: '',
    password_confirmation: '',
    date_naissance: '',
    lieu_naissance: '',
    ville: '',
    code_postal: '',
    adresse: '',
    mobile: '',
    tel: '',
    num_permis: '',
    date_permis: '',
    lieu_permis: '',
  });

  const submit: FormEventHandler = (e) => {
    e.preventDefault();
    post(route('register'), {
      onFinish: () => reset('password', 'password_confirmation'),
    });
  };

  return (
    <AuthLayout title="Créer un compte" description="Entrez vos informations ci-dessous pour créer votre compte" width="lg">
      <Head title="Inscription" />
      <form className="flex flex-col justify-center " onSubmit={submit}>
        <div className="gap-6 mx-auto xl:w-[700px]">
          <Card className="m-3">
            <CardHeader>
              <CardTitle>Vos coordonnées</CardTitle>
              <CardDescription>Informations personnelles requises pour l'inscription</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid gap-4">
                <div className="space-y-2">
                  <Label htmlFor="civilite">
                    Civilité<span className="text-red-500">*</span>
                  </Label>
                  <Select value={data.civilite} onValueChange={(value) => setData('civilite', value)} required>
                    <SelectTrigger id="civilite">
                      <SelectValue placeholder="Sélectionner une civilité" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Monsieur">Monsieur</SelectItem>
                      <SelectItem value="Madame">Madame</SelectItem>
                      <SelectItem value="Mademoiselle">Mademoiselle</SelectItem>
                    </SelectContent>
                  </Select>
                  <InputError message={errors.civilite} />
                </div>
              </div>

              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <Label htmlFor="nom">
                    Nom<span className="text-red-500">*</span>
                  </Label>
                  <Input id="nom" type="text" required value={data.nom} onChange={(e) => setData('nom', e.target.value)} disabled={processing} />
                  <InputError message={errors.nom} />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="prenom">
                    Prénom<span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="prenom"
                    type="text"
                    required
                    value={data.prenom}
                    onChange={(e) => setData('prenom', e.target.value)}
                    disabled={processing}
                  />
                  <InputError message={errors.prenom} />
                </div>
              </div>

              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <Label htmlFor="date_naissance">
                    Date de naissance<span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="date_naissance"
                    type="date"
                    required
                    value={data.date_naissance}
                    onChange={(e) => setData('date_naissance', e.target.value)}
                    disabled={processing}
                  />
                  <InputError message={errors.date_naissance} />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="lieu_naissance">
                    Lieu de naissance<span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="lieu_naissance"
                    type="text"
                    required
                    value={data.lieu_naissance}
                    onChange={(e) => setData('lieu_naissance', e.target.value)}
                    disabled={processing}
                  />
                  <InputError message={errors.lieu_naissance} />
                </div>
              </div>

              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <Label htmlFor="ville">
                    Ville<span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="ville"
                    type="text"
                    required
                    value={data.ville}
                    onChange={(e) => setData('ville', e.target.value)}
                    disabled={processing}
                  />
                  <InputError message={errors.ville} />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="code_postal">
                    Code postal<span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="code_postal"
                    type="text"
                    required
                    maxLength={5}
                    value={data.code_postal}
                    onChange={(e) => setData('code_postal', e.target.value)}
                    disabled={processing}
                  />
                  <InputError message={errors.code_postal} />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="adresse">
                  Adresse<span className="text-red-500">*</span>
                </Label>
                <Input
                  id="adresse"
                  type="text"
                  required
                  value={data.adresse}
                  onChange={(e) => setData('adresse', e.target.value)}
                  disabled={processing}
                />
                <InputError message={errors.adresse} />
              </div>

              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <Label htmlFor="mobile">
                    Téléphone mobile<span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="mobile"
                    type="tel"
                    required
                    maxLength={10}
                    value={data.mobile}
                    onChange={(e) => setData('mobile', e.target.value)}
                    disabled={processing}
                  />
                  <InputError message={errors.mobile} />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="tel">Téléphone fixe</Label>
                  <Input id="tel" type="tel" maxLength={10} value={data.tel} onChange={(e) => setData('tel', e.target.value)} disabled={processing} />
                  <InputError message={errors.tel} />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="email">
                  Adresse email<span className="text-red-500">*</span>
                </Label>
                <Input
                  id="email"
                  type="email"
                  required
                  autoComplete="email"
                  value={data.email}
                  onChange={(e) => setData('email', e.target.value)}
                  disabled={processing}
                  placeholder="<EMAIL>"
                />
                <InputError message={errors.email} />
              </div>

              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <Label htmlFor="password">
                    Mot de passe<span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="password"
                    type="password"
                    required
                    autoComplete="new-password"
                    value={data.password}
                    onChange={(e) => setData('password', e.target.value)}
                    disabled={processing}
                  />
                  <InputError message={errors.password} />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="password_confirmation">
                    Confirmer le mot de passe<span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="password_confirmation"
                    type="password"
                    required
                    autoComplete="new-password"
                    value={data.password_confirmation}
                    onChange={(e) => setData('password_confirmation', e.target.value)}
                    disabled={processing}
                  />
                  <InputError message={errors.password_confirmation} />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="m-3">
            <CardHeader>
              <CardTitle>Votre Permis</CardTitle>
              <CardDescription>Informations concernant votre permis de conduire</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-2">
                <Label htmlFor="num_permis">Numéro de permis</Label>
                <Input
                  id="num_permis"
                  type="text"
                  value={data.num_permis}
                  onChange={(e) => setData('num_permis', e.target.value)}
                  disabled={processing}
                />
                <InputError message={errors.num_permis} />
              </div>

              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <Label htmlFor="date_permis">Date du permis</Label>
                  <Input
                    id="date_permis"
                    type="date"
                    value={data.date_permis}
                    onChange={(e) => setData('date_permis', e.target.value)}
                    disabled={processing}
                  />
                  <InputError message={errors.date_permis} />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="lieu_permis">Lieu du permis</Label>
                  <Input
                    id="lieu_permis"
                    type="text"
                    value={data.lieu_permis}
                    onChange={(e) => setData('lieu_permis', e.target.value)}
                    disabled={processing}
                  />
                  <InputError message={errors.lieu_permis} />
                </div>
              </div>
            </CardContent>
          </Card>
          <div className="m-3 flex justify-between gap-4">
            <Button type="button" variant="outline" onClick={() => window.history.back()}>
              Annuler
            </Button>
            <Button type="submit" disabled={processing}>
              {processing && <LoaderCircle className="mr-2 h-4 w-4 animate-spin" />}
              Créer un compte
            </Button>
          </div>
        </div>

        <div className="text-muted-foreground text-center text-sm">
          Vous avez déjà un compte ? <TextLink href={route('login')}>Se connecter</TextLink>
        </div>
      </form>
    </AuthLayout>
  );
}
