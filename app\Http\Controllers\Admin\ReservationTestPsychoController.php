<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Imports\ReservationsTestPsychoImport;
use App\Models\ReservationTestPsycho;
use App\Models\TestPsycho;
use App\Models\TypeTestPsycho;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Inertia\Inertia;
use Maatwebsite\Excel\Facades\Excel;

class ReservationTestPsychoController extends Controller
{
    public function index()
    {
        return Inertia::render('Admin/ReservationsTestsPsychos/Index', [
            'reservations' => ReservationTestPsycho::with(['testPsycho.lieu.ville.departement', 'user', 'typeTestPsycho'])
                ->orderBy('date_reservation', 'desc')
                ->paginate(10),
            'tests' => TestPsycho::with('lieu')->get(),
            'typeTests' => TypeTestPsycho::all(),
            'users' => User::where('role', 'client')->get()
        ]);
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'test_psycho_id' => 'required|exists:test_psychos,id',
            'user_id' => 'required|exists:users,id',
            'type_test_psycho_id' => 'required|exists:type_test_psychos,id',
            'date_reservation' => 'required|date',
            'statut' => 'required|in:confirmée,en attente,annulée',
            'motif' => 'nullable|string|max:255',
            'permis_recto' => 'nullable|string|max:255',
            'permis_verso' => 'nullable|string|max:255',
            'document_tribunal' => 'nullable|string|max:255',
            'permis_recto_file' => 'nullable|file|mimes:jpeg,png,jpg,pdf|max:2048',
            'permis_verso_file' => 'nullable|file|mimes:jpeg,png,jpg,pdf|max:2048',
            'document_tribunal_file' => 'nullable|file|mimes:jpeg,png,jpg,pdf|max:2048',
        ]);

        // Supprimer les champs de fichiers de $validated
        $data = collect($validated)->except(['permis_recto_file', 'permis_verso_file', 'document_tribunal_file'])->toArray();

        // Gérer les fichiers téléchargés
        if ($request->hasFile('permis_recto_file')) {
            $path = $request->file('permis_recto_file')->store('permis', 'public');
            $data['permis_recto'] = $path;
        }

        if ($request->hasFile('permis_verso_file')) {
            $path = $request->file('permis_verso_file')->store('permis', 'public');
            $data['permis_verso'] = $path;
        }

        if ($request->hasFile('document_tribunal_file')) {
            $path = $request->file('document_tribunal_file')->store('documents', 'public');
            $data['document_tribunal'] = $path;
        }

        ReservationTestPsycho::create($data);

        return redirect()->back()->with('success', 'Réservation de test psychotechnique créée avec succès.');
    }

    public function update(Request $request, ReservationTestPsycho $reservationsTestPsycho)
    {
        $validated = $request->validate([
            'test_psycho_id' => 'required|exists:test_psychos,id',
            'user_id' => 'required|exists:users,id',
            'type_test_psycho_id' => 'required|exists:type_test_psychos,id',
            'date_reservation' => 'required|date',
            'statut' => 'required|in:confirmée,en attente,annulée',
            'motif' => 'nullable|string|max:255',
            'permis_recto' => 'nullable|string|max:255',
            'permis_verso' => 'nullable|string|max:255',
            'document_tribunal' => 'nullable|string|max:255',
            'permis_recto_file' => 'nullable|file|mimes:jpeg,png,jpg,pdf|max:2048',
            'permis_verso_file' => 'nullable|file|mimes:jpeg,png,jpg,pdf|max:2048',
            'document_tribunal_file' => 'nullable|file|mimes:jpeg,png,jpg,pdf|max:2048',
        ]);

        // Supprimer les champs de fichiers de $validated
        $data = collect($validated)->except(['permis_recto_file', 'permis_verso_file', 'document_tribunal_file'])->toArray();

        // Gérer les fichiers téléchargés
        if ($request->hasFile('permis_recto_file')) {
            $path = $request->file('permis_recto_file')->store('permis', 'public');
            $data['permis_recto'] = $path;
        }

        if ($request->hasFile('permis_verso_file')) {
            $path = $request->file('permis_verso_file')->store('permis', 'public');
            $data['permis_verso'] = $path;
        }

        if ($request->hasFile('document_tribunal_file')) {
            $path = $request->file('document_tribunal_file')->store('documents', 'public');
            $data['document_tribunal'] = $path;
        }

        $reservationsTestPsycho->update($data);

        return redirect()->back()->with('success', 'Réservation de test psychotechnique mise à jour avec succès.');
    }

    public function destroy(ReservationTestPsycho $reservationsTestPsycho)
    {
        $reservationsTestPsycho->delete();
        return redirect()->back()->with('success', 'Réservation de test psychotechnique supprimée avec succès.');
    }

    /**
     * Importer des réservations de tests psychotechniques depuis un fichier Excel
     */
    public function import(Request $request)
    {
        $request->validate([
            'file' => 'required|file|mimes:xlsx,xls',
        ], [
            'file.required' => 'Veuillez sélectionner un fichier Excel',
            'file.file' => 'Le fichier doit être un fichier valide',
            'file.mimes' => 'Le fichier doit être au format Excel (.xlsx ou .xls)',
        ]);

        try {
            // Enregistrer le fichier temporairement pour le déboguer si nécessaire
            $path = $request->file('file')->store('temp');
            Log::info('Fichier importé: ' . $path);

            // Importer les données
            $import = new ReservationsTestPsychoImport();
            Excel::import($import, $request->file('file'));

            // Récupérer les statistiques
            $newUsersCount = $import->getNewUsersCount();
            $existingUsersCount = $import->getExistingUsersCount();
            $reservationsCount = $import->getReservationsCount();

            // Message de succès avec les statistiques
            $message = "Importation réussie: $reservationsCount réservations de tests importées, ";
            $message .= "$newUsersCount nouveaux utilisateurs créés, ";
            $message .= "$existingUsersCount utilisateurs existants trouvés.";

            return redirect()->back()->with('success', $message);
        } catch (\Maatwebsite\Excel\Validators\ValidationException $e) {
            // Erreurs de validation
            $failures = $e->failures();
            $errors = [];

            foreach ($failures as $failure) {
                $errors[] = "Ligne {$failure->row()}: {$failure->errors()[0]}";
            }

            return redirect()->back()->withErrors(['file' => $errors]);
        } catch (\Exception $e) {
            Log::error('Erreur lors de l\'importation des réservations de tests: ' . $e->getMessage());
            return redirect()->back()->withErrors(['file' => 'Une erreur est survenue lors de l\'importation: ' . $e->getMessage()]);
        }
    }
}
