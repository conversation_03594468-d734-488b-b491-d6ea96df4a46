import { Head } from '@inertiajs/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import ClientLayout from '@/layouts/client-layout';
import { Reservation } from '@/types';
import { format } from 'date-fns';
import { fr } from 'date-fns/locale';
import { Badge } from '@/components/ui/badge';
import { CalendarDays, MapPin, Users } from 'lucide-react';

interface StagesProps {
  reservations: Reservation[];
}

export default function Stages({ reservations = [] }: StagesProps) {
  // Filtrer uniquement les réservations confirmées
  const confirmedReservations = reservations.filter(r => r.statut === 'confirmée');

  return (
    <ClientLayout title="Mes stages">
      <Head title="Mes stages" />

      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-bold tracking-tight">Mes stages</h1>
        </div>

        {confirmedReservations.length === 0 ? (
          <Card>
            <CardContent className="py-10 text-center">
              <p className="text-muted-foreground">Vous n'avez pas encore de stages confirmés.</p>
            </CardContent>
          </Card>
        ) : (
          <div className="space-y-4">
            {confirmedReservations.map((reservation) => {
              const stage = reservation.stage;
              const dateDebut = new Date(stage.date_debut);
              const dateFin = new Date(stage.date_fin);
              const isUpcoming = dateDebut > new Date();

              return (
                <Card key={reservation.id} className={isUpcoming ? '' : 'opacity-70'}>
                  <CardHeader className="pb-2">
                    <div className="flex items-center justify-between">
                      <CardTitle>
                        Stage du {format(dateDebut, 'dd MMMM yyyy', { locale: fr })}
                      </CardTitle>
                      {isUpcoming ? (
                        <Badge className="bg-green-500">À venir</Badge>
                      ) : (
                        <Badge variant="outline">Terminé</Badge>
                      )}
                    </div>
                    <CardDescription>
                      {reservation.type_stage.nom}
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="grid gap-4">
                      <div className="flex items-center gap-2">
                        <CalendarDays className="h-4 w-4 text-muted-foreground" />
                        <span>
                          Du {format(dateDebut, 'dd/MM/yyyy', { locale: fr })} au {format(dateFin, 'dd/MM/yyyy', { locale: fr })}
                        </span>
                      </div>
                      <div className="flex items-center gap-2">
                        <MapPin className="h-4 w-4 text-muted-foreground" />
                        <span>
                          {stage.lieu.nom} - {stage.lieu.adresse}, {stage.lieu.ville.nom}
                        </span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Users className="h-4 w-4 text-muted-foreground" />
                        <span>
                          {stage.places_disponibles} places disponibles
                        </span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        )}
      </div>
    </ClientLayout>
  );
}
