import { cn } from "@/lib/utils";
import { motion, useMotionValue, useTransform, animate } from "framer-motion";
import { useEffect } from "react";

const formatNumber = (num: number) => {
  // Convert number to string and split into array of characters
  const numStr = Math.round(num).toString();
  const parts = [];

  // Process from right to left, adding parts in reverse
  for (let i = numStr.length - 1; i >= 0; i -= 3) {
    const start = Math.max(0, i - 2);
    parts.unshift(numStr.slice(start, i + 1));
  }

  // Join with thin spaces
  return parts.join(',');
};

export default function CountAnimation({
  number,
  className,
}: {
  number: number;
  className: string;
}) {
  const count = useMotionValue(0);
  const rounded = useTransform(count, (latest) => formatNumber(latest));

  useEffect(() => {
    const animation = animate(count, number, {
      duration: 2,
      ease: "easeOut"
    });

    return animation.stop;
  }, []);

  return <motion.h1 className={cn("font-mono tracking-tight", className)}>{rounded}</motion.h1>;
}





