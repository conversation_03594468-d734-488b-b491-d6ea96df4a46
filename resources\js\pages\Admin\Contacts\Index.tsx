import { Head, router } from '@inertiajs/react';
import { PageProps, BreadcrumbItem, PaginatedData } from '@/types';
import AppLayout from '@/layouts/app-layout';
import { useState } from 'react';
import { ColumnDef } from '@tanstack/react-table';
import { DataTable, DataTableColumnHeader } from '@/components/ui/data-table';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { format, parseISO } from 'date-fns';
import { fr } from 'date-fns/locale';
import { Eye, Trash2, CheckCircle, XCircle } from 'lucide-react';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import { useForm } from '@inertiajs/react';

interface Contact {
  id: number;
  nom: string;
  prenom: string;
  email: string;
  telephone: string | null;
  sujet: 'stage' | 'test' | 'pssm' | 'autre';
  message: string;
  lu: boolean;
  reponse: string | null;
  date_reponse: string | null;
  admin_id: number | null;
  admin?: {
    id: number;
    nom: string;
    prenom: string;
  } | null;
  created_at: string;
  updated_at: string;
}

interface ContactsPageProps extends PageProps {
  contacts: PaginatedData<Contact>;
  filters: {
    lu?: string | null;
    sujet?: string | null;
    search?: string | null;
  };
}

export default function Index({ contacts, filters }: ContactsPageProps) {
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [contactToDelete, setContactToDelete] = useState<Contact | null>(null);
  const [isReplyDialogOpen, setIsReplyDialogOpen] = useState(false);
  const [contactToReply, setContactToReply] = useState<Contact | null>(null);

  const { data, setData, post, processing, errors } = useForm({
    reponse: '',
  });

  const handleView = (contact: Contact) => {
    router.visit(route('admin.contacts.show', contact.id));
  };

  const handleDelete = (contact: Contact) => {
    setContactToDelete(contact);
    setIsDeleteDialogOpen(true);
  };

  const confirmDelete = () => {
    if (contactToDelete) {
      router.delete(route('admin.contacts.destroy', contactToDelete.id), {
        onSuccess: () => {
          setIsDeleteDialogOpen(false);
          setContactToDelete(null);
        },
      });
    }
  };

  const handleReply = (contact: Contact) => {
    setContactToReply(contact);
    setData('reponse', contact.reponse || '');
    setIsReplyDialogOpen(true);
  };

  const submitReply = () => {
    if (contactToReply) {
      post(route('admin.contacts.update', contactToReply.id), {
        onSuccess: () => {
          setIsReplyDialogOpen(false);
          setContactToReply(null);
          setData('reponse', '');
        },
      });
    }
  };

  const toggleReadStatus = (contact: Contact) => {
    router.patch(route('admin.contacts.toggle-read', contact.id));
  };

  const getSujetLabel = (sujet: string) => {
    switch (sujet) {
      case 'stage':
        return 'Stage de récupération de points';
      case 'test':
        return 'Test psychotechnique';
      case 'pssm':
        return 'Formation PSSM';
      case 'autre':
        return 'Autre demande';
      default:
        return sujet;
    }
  };

  const columns: ColumnDef<Contact>[] = [
    {
      accessorKey: 'lu',
      header: ({ column }) => <DataTableColumnHeader column={column} title="Statut" />,
      cell: ({ row }) => (
        <div className="flex justify-center">
          <Badge 
            variant={row.original.lu ? 'outline' : 'default'} 
            className="cursor-pointer"
            onClick={() => toggleReadStatus(row.original)}
          >
            {row.original.lu ? 'Lu' : 'Non lu'}
          </Badge>
        </div>
      ),
      enableSorting: true,
    },
    {
      accessorKey: 'nom_complet',
      header: ({ column }) => <DataTableColumnHeader column={column} title="Expéditeur" />,
      cell: ({ row }) => (
        <div>
          <div className="font-medium">{row.original.prenom} {row.original.nom}</div>
          <div className="text-sm text-muted-foreground">{row.original.email}</div>
          {row.original.telephone && (
            <div className="text-sm text-muted-foreground">{row.original.telephone}</div>
          )}
        </div>
      ),
    },
    {
      accessorKey: 'sujet',
      header: ({ column }) => <DataTableColumnHeader column={column} title="Sujet" />,
      cell: ({ row }) => getSujetLabel(row.original.sujet),
      enableSorting: true,
    },
    {
      accessorKey: 'created_at',
      header: ({ column }) => <DataTableColumnHeader column={column} title="Date" />,
      cell: ({ row }) => format(parseISO(row.original.created_at), 'dd/MM/yyyy HH:mm', { locale: fr }),
      enableSorting: true,
    },
    {
      accessorKey: 'reponse',
      header: ({ column }) => <DataTableColumnHeader column={column} title="Réponse" />,
      cell: ({ row }) => (
        <div className="flex justify-center">
          {row.original.reponse ? (
            <Badge variant="success">Répondu</Badge>
          ) : (
            <Badge variant="destructive">En attente</Badge>
          )}
        </div>
      ),
    },
    {
      id: 'actions',
      cell: ({ row }) => (
        <div className="flex justify-end gap-2">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => handleView(row.original)}
            title="Voir le message"
          >
            <Eye className="h-4 w-4" />
          </Button>
          <Button
            variant={row.original.reponse ? 'ghost' : 'default'}
            size="icon"
            onClick={() => handleReply(row.original)}
            title={row.original.reponse ? 'Modifier la réponse' : 'Répondre'}
          >
            <CheckCircle className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="icon"
            onClick={() => handleDelete(row.original)}
            title="Supprimer"
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      ),
    },
  ];

  const breadcrumbs: BreadcrumbItem[] = [
    {
      title: 'Messages de contact',
      href: route('admin.contacts.index'),
    },
  ];

  return (
    <AppLayout breadcrumbs={breadcrumbs}>
      <Head title="Messages de contact" />
      <div className="flex h-full flex-1 flex-col gap-4 rounded-xl p-4">
        <DataTable
          title="Messages de contact"
          columns={columns}
          data={contacts.data}
          pagination={{
            links: contacts.links,
            from: contacts.from,
            to: contacts.to,
            total: contacts.total
          }}
          searchableColumns={[
            {
              id: 'nom_complet',
              title: 'Expéditeur'
            },
            {
              id: 'email',
              title: 'Email'
            }
          ]}
          filterableColumns={[
            {
              id: 'lu',
              title: 'Statut',
              options: [
                { value: 'true', label: 'Lu' },
                { value: 'false', label: 'Non lu' }
              ]
            },
            {
              id: 'sujet',
              title: 'Sujet',
              options: [
                { value: 'stage', label: 'Stage de récupération de points' },
                { value: 'test', label: 'Test psychotechnique' },
                { value: 'pssm', label: 'Formation PSSM' },
                { value: 'autre', label: 'Autre demande' }
              ]
            }
          ]}
        />
      </div>

      {/* Dialog de confirmation de suppression */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirmer la suppression</DialogTitle>
          </DialogHeader>
          <p>
            Êtes-vous sûr de vouloir supprimer ce message de contact ? Cette action est irréversible.
          </p>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
              Annuler
            </Button>
            <Button variant="destructive" onClick={confirmDelete}>
              Supprimer
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Dialog de réponse */}
      <Dialog open={isReplyDialogOpen} onOpenChange={setIsReplyDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>
              {contactToReply?.reponse ? 'Modifier la réponse' : 'Répondre au message'}
            </DialogTitle>
          </DialogHeader>
          
          {contactToReply && (
            <div className="space-y-4">
              <div className="rounded-md bg-muted p-4">
                <h4 className="font-medium">Message original :</h4>
                <p className="mt-2 whitespace-pre-wrap">{contactToReply.message}</p>
                <div className="mt-2 text-sm text-muted-foreground">
                  De : {contactToReply.prenom} {contactToReply.nom} ({contactToReply.email})
                </div>
              </div>
              
              <div>
                <label htmlFor="reponse" className="block text-sm font-medium mb-1">
                  Votre réponse :
                </label>
                <Textarea
                  id="reponse"
                  value={data.reponse}
                  onChange={(e) => setData('reponse', e.target.value)}
                  rows={8}
                  className={errors.reponse ? 'border-red-500' : ''}
                />
                {errors.reponse && (
                  <p className="mt-1 text-sm text-red-500">{errors.reponse}</p>
                )}
              </div>
            </div>
          )}
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsReplyDialogOpen(false)}>
              Annuler
            </Button>
            <Button onClick={submitReply} disabled={processing}>
              {processing ? 'Envoi en cours...' : 'Envoyer la réponse'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </AppLayout>
  );
}
