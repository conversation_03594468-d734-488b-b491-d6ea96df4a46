import DataTable from '@/components/DataTable';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import AppLayout from '@/layouts/app-layout';
import { BreadcrumbItem, Departement, Lieu, PageProps, PaginatedData, Ville } from '@/types';
import { Head, router, useForm } from '@inertiajs/react';
import { useMemo, useState } from 'react';

interface LieuxPageProps extends PageProps {
    lieux: PaginatedData<Lieu>;
    villes: Ville[];
    departements: Departement[];
}

export default function Index({ lieux, villes, departements }: LieuxPageProps) {
    const [isOpen, setIsOpen] = useState(false);
    const [editingLieu, setEditingLieu] = useState<Lieu | null>(null);
    const [departementId, setDepartementId] = useState<string>('');

    const form = useForm({
        nom: '',
        adresse: '',
        ville_id: '',
    });

    const columns = [
        { key: 'nom', label: 'Nom' },
        { key: 'adresse', label: 'Adresse' },
        {
            key: 'ville',
            label: 'Ville',
            render: (value: unknown, row: Record<string, unknown>) => (row as unknown as Lieu).ville?.nom || '',
        },
        {
            key: 'stages',
            label: 'Stages',
            render: (value: unknown, row: Record<string, unknown>) => (row as unknown as Lieu).stages?.length || 0,
        },
    ];

    const handleAdd = () => {
        setEditingLieu(null);
        form.reset();
        form.setData({ nom: '', adresse: '', ville_id: '' });
        setIsOpen(true);
    };

    const handleEdit = (row: Record<string, unknown>) => {
        const lieu = row as unknown as Lieu;
        setEditingLieu(lieu);
        setDepartementId(lieu.ville?.departement_id ? String(lieu.ville.departement_id) : '');
        form.setData({
            nom: lieu.nom,
            adresse: lieu.adresse,
            ville_id: lieu.ville ? String(lieu.ville.id) : '',
        });
        setIsOpen(true);
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        if (editingLieu) {
            form.put(route('admin.lieux.update', editingLieu.id), { onSuccess: () => setIsOpen(false) });
        } else {
            form.post(route('admin.lieux.store'), { onSuccess: () => setIsOpen(false) });
        }
    };

    const handleDelete = (row: Record<string, unknown>) => {
        const lieu = row as unknown as Lieu;
        if (confirm('Êtes-vous sûr de vouloir supprimer ce lieu ?')) {
            router.delete(route('admin.lieux.destroy', lieu.id));
        }
    };

    const breadcrumbs: BreadcrumbItem[] = [{ title: 'Lieux', href: '/admin/lieux' }];

    // Filtered villes based on selected department
    const filteredVilles = useMemo(() => {
        // Filter villes based on selected department
        return departementId ? villes.filter((v) => String(v.departement_id) === departementId) : [];
    }, [departementId, villes]);



    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Lieux" />
            <div className="flex h-full flex-1 flex-col gap-4 rounded-xl p-4">
                <DataTable
                    title="Lieux"
                    columns={columns}
                    data={lieux.data.map((lieu) => ({ id: lieu.id, nom: lieu.nom, adresse: lieu.adresse, ville: lieu.ville, stages: lieu.stages }))}
                    onAdd={handleAdd}
                    onEdit={handleEdit}
                    onDelete={handleDelete}
                    pagination={{ links: lieux.links, from: lieux.from, to: lieux.to, total: lieux.total }}
                />
            </div>

            <Dialog open={isOpen} onOpenChange={setIsOpen}>
                <DialogContent>
                    <DialogHeader>
                        <DialogTitle>{editingLieu ? 'Modifier le lieu' : 'Ajouter un lieu'}</DialogTitle>
                        <DialogDescription>
                            {editingLieu ? 'Modifiez les informations du lieu ci-dessous.' : 'Remplissez le formulaire pour ajouter un nouveau lieu.'}
                        </DialogDescription>
                    </DialogHeader>
                    <form onSubmit={handleSubmit} className="space-y-4">
                        <Input placeholder="Nom du lieu" value={form.data.nom} onChange={(e) => form.setData('nom', e.target.value)} />
                        <Textarea
                            placeholder="Adresse complète"
                            value={form.data.adresse}
                            onChange={(e) => form.setData('adresse', e.target.value)}
                        />

                        <Select
                            value={departementId}
                            onValueChange={(value) => {
                                setDepartementId(value);
                                form.setData('ville_id', '');
                            }}
                        >
                            <SelectTrigger>
                                <SelectValue placeholder="Sélectionner un département" />
                            </SelectTrigger>
                            <SelectContent>
                                {departements.map((dep) => (
                                    <SelectItem key={dep.id} value={String(dep.id)}>
                                        {dep.nom}
                                    </SelectItem>
                                ))}
                            </SelectContent>
                        </Select>

                        <Select
                            value={form.data.ville_id}
                            onValueChange={(value) => form.setData('ville_id', value)}
                            disabled={!departementId}
                        >
                            <SelectTrigger>
                                <SelectValue placeholder="Sélectionner une ville" />
                            </SelectTrigger>
                            <SelectContent>
                                {filteredVilles.map((v) => (
                                    <SelectItem key={v.id} value={String(v.id)}>
                                        {v.nom}
                                    </SelectItem>
                                ))}
                            </SelectContent>
                        </Select>

                        <div className="flex justify-end space-x-2">
                            <Button type="button" variant="outline" onClick={() => setIsOpen(false)}>
                                Annuler
                            </Button>
                            <Button type="submit" disabled={form.processing}>
                                {editingLieu ? 'Modifier' : 'Ajouter'}
                            </Button>
                        </div>
                    </form>
                </DialogContent>
            </Dialog>
        </AppLayout>
    );
}
