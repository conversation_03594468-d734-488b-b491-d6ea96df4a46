import { NavFooter } from '@/components/nav-footer';
import { NavMainWithDropdown } from '@/components/nav-main-with-dropdown';
import { NavUser } from '@/components/nav-user';
import { Sidebar, SidebarContent, SidebarFooter, SidebarHeader, SidebarMenu, SidebarMenuButton, SidebarMenuItem } from '@/components/ui/sidebar';
import { type NavItem } from '@/types';
import { Link } from '@inertiajs/react';
import { Calendar, Building2, MapPin, Users, LayoutGrid, Settings, BookOpen, UserCircle, MessageSquare } from 'lucide-react';
import AppLogo from './app-logo';

const mainNavItems: NavItem[] = [
    {
        title: 'Platform',
        type: 'group',
    },
    {
        title: 'Dashboard',
        href: '/admin/dashboard',
        icon: LayoutGrid,
    },
    {
        title: 'Stages Permis',
        type: 'group',
    },
    {
        title: 'Stages',
        href: '/admin/stages',
        icon: Calendar,
    },
    {
        title: 'Réservations',
        href: '/admin/reservations',
        icon: BookOpen,
    },
    {
        title: 'Tests Psychologiques',
        type: 'group',
    },
    {
        title: 'Tests Psychotechniques',
        href: '/admin/tests-psychos',
        icon: Calendar,
    },
    {
        title: 'Réservations Tests',
        href: '/admin/reservations-tests-psychos',
        icon: BookOpen,
    },

    {
        title: 'Utilisateurs',
        type: 'group',
    },
    {
        title: 'Clients',
        href: '/admin/clients',
        icon: UserCircle,
    },
    {
        title: 'Messages',
        href: '/admin/contacts',
        icon: MessageSquare,
    },

    {
        title: 'Configuration',
        type: 'group',
    },
    {
        title: 'Configuration Générale',
        href: '/admin/configuration',
        icon: Settings,
    },

    {
        title: '----',
        type: 'group',
    },
    {
        title: 'Paramétrage',
        icon: Settings,
        children: [
            {
                title: 'Départements',
                href: '/admin/departements',
                icon: Building2,
            },
            {
                title: 'Villes',
                href: '/admin/villes',
                icon: MapPin,
            },
            {
                title: 'Lieux',
                href: '/admin/lieux',
                icon: MapPin,
            },
            {
                title: 'Types de stages',
                href: '/admin/types-stages',
                icon: Users,
            },
            {
                title: 'Types de tests psycho',
                href: '/admin/types-tests-psychos',
                icon: Users,
            },
        ],
    },
];

const footerNavItems: NavItem[] = [
    // {
    //     title: 'Repository',
    //     href: 'https://github.com/laravel/react-starter-kit',
    //     icon: Folder,
    // },
    // {
    //     title: 'Documentation',
    //     href: 'https://laravel.com/docs/starter-kits',
    //     icon: BookOpen,
    // },
];

export function AppSidebar() {
    return (
        <Sidebar collapsible="icon" variant="inset">
            <SidebarHeader>
                <SidebarMenu>
                    <SidebarMenuItem>
                        <SidebarMenuButton size="lg" asChild>
                            <Link href="/admin/dashboard" prefetch>
                                <AppLogo />
                            </Link>
                        </SidebarMenuButton>
                    </SidebarMenuItem>
                </SidebarMenu>
            </SidebarHeader>

            <SidebarContent>
                <NavMainWithDropdown items={mainNavItems} />
            </SidebarContent>

            <SidebarFooter>
                <NavFooter items={footerNavItems} className="mt-auto" />
                <NavUser />
            </SidebarFooter>
        </Sidebar>
    );
}

