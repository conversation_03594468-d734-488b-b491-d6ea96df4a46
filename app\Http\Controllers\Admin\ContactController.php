<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Contact;
use App\Notifications\ContactReplyNotification;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Notification;
use Inertia\Inertia;

class ContactController extends Controller
{
    /**
     * Display a listing of the contact messages.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Inertia\Response
     */
    public function index(Request $request)
    {
        $query = Contact::with('admin')->latest();

        // Filtrer par statut (lu/non lu)
        if ($request->has('lu') && $request->lu !== null) {
            $query->where('lu', $request->lu === 'true');
        }

        // Filtrer par sujet
        if ($request->has('sujet') && $request->sujet !== 'all') {
            $query->where('sujet', $request->sujet);
        }

        // Recherche par nom, prénom ou email
        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('nom', 'like', "%{$search}%")
                    ->orWhere('prenom', 'like', "%{$search}%")
                    ->orWhere('email', 'like', "%{$search}%");
            });
        }

        $contacts = $query->paginate(10)
            ->withQueryString();

        return Inertia::render('Admin/Contacts/Index', [
            'contacts' => $contacts,
            'filters' => $request->only(['lu', 'sujet', 'search']),
        ]);
    }

    /**
     * Display the specified contact message.
     *
     * @param  \App\Models\Contact  $contact
     * @return \Inertia\Response
     */
    public function show(Contact $contact)
    {
        // Marquer comme lu si ce n'est pas déjà le cas
        if (!$contact->lu) {
            $contact->update(['lu' => true]);
        }

        return Inertia::render('Admin/Contacts/Show', [
            'contact' => $contact->load('admin'),
        ]);
    }

    /**
     * Update the specified contact message in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Contact  $contact
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(Request $request, Contact $contact)
    {
        $request->validate([
            'reponse' => 'required|string',
        ], [
            'reponse.required' => 'La réponse est obligatoire.',
        ]);

        $contact->update([
            'reponse' => $request->reponse,
            'date_reponse' => now(),
            'admin_id' => Auth::id(),
        ]);

        // Envoyer une notification à l'utilisateur
        Notification::route('mail', $contact->email)
            ->notify(new ContactReplyNotification($contact));

        return redirect()->route('admin.contacts.index')
            ->with('success', 'Réponse envoyée avec succès.');
    }

    /**
     * Remove the specified contact message from storage.
     *
     * @param  \App\Models\Contact  $contact
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy(Contact $contact)
    {
        $contact->delete();

        return redirect()->route('admin.contacts.index')
            ->with('success', 'Message supprimé avec succès.');
    }

    /**
     * Mark the specified contact message as read/unread.
     *
     * @param  \App\Models\Contact  $contact
     * @return \Illuminate\Http\RedirectResponse
     */
    public function toggleRead(Contact $contact)
    {
        $contact->update([
            'lu' => !$contact->lu,
        ]);

        return back()->with('success', $contact->lu ? 'Message marqué comme lu.' : 'Message marqué comme non lu.');
    }
}
