<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('reservations', function (Blueprint $table) {
            $table->id();
            $table->foreignId('stage_id')->constrained()->onDelete('cascade');
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('type_stage_id')->constrained('type_stages')->onDelete('cascade');
            $table->timestamp('date_reservation');
            $table->enum('statut', ['confirmée', 'en attente', 'annulée']);
            $table->date('date_infraction')->nullable();
            $table->time('heure_infraction')->nullable();
            $table->string('lieu_infraction')->nullable();
            $table->string('permis_recto')->nullable();
            $table->string('permis_verso')->nullable();
            $table->string('lettre_48n_recto')->nullable();
            $table->string('lettre_48n_verso')->nullable();
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('reservations');
    }
};
