<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Contact extends Model
{
    use HasFactory;

    protected $fillable = [
        'nom',
        'prenom',
        'email',
        'telephone',
        'sujet',
        'message',
        'lu',
        'reponse',
        'date_reponse',
        'admin_id'
    ];

    protected $casts = [
        'lu' => 'boolean',
        'date_reponse' => 'datetime',
    ];

    /**
     * Get the admin that responded to this contact message.
     */
    public function admin()
    {
        return $this->belongsTo(User::class, 'admin_id');
    }
}
