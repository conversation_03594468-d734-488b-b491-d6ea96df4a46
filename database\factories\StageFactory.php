<?php

namespace Database\Factories;

use App\Models\Lieu;
use Illuminate\Database\Eloquent\Factories\Factory;

class StageFactory extends Factory
{
    public function definition(): array
    {
        $date_debut = fake()->dateTimeBetween('now', '+6 months');
        return [
            'date_debut' => $date_debut,
            'date_fin' => fake()->dateTimeBetween($date_debut, $date_debut->format('Y-m-d H:i:s') . ' +2 days'),
            'lieu_id' => Lieu::factory(),
            'places_disponibles' => fake()->numberBetween(10, 30),
            'prix' => fake()->randomFloat(2, 150, 300),
            'reference' => 'STAGE-' . fake()->unique()->numberBetween(1000, 9999),
        ];
    }
}