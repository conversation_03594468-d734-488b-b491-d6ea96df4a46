#!/bin/bash

# Laravel Production Email Setup Script
# This script helps configure email settings for production deployment

echo "🚀 Laravel Production Email Setup"
echo "=================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if .env file exists
if [ ! -f .env ]; then
    print_error ".env file not found!"
    echo "Please copy .env.production.example to .env and configure it first."
    exit 1
fi

print_status "Found .env file"

# Check if required email variables are set
check_env_var() {
    local var_name=$1
    local var_value=$(grep "^$var_name=" .env | cut -d '=' -f2)
    
    if [ -z "$var_value" ] || [ "$var_value" = "<EMAIL>" ] || [ "$var_value" = "your-password" ]; then
        print_error "$var_name is not properly configured in .env"
        return 1
    else
        print_status "$var_name is configured"
        return 0
    fi
}

echo ""
echo "🔍 Checking email configuration..."

# Check required email variables
REQUIRED_VARS=("MAIL_MAILER" "MAIL_HOST" "MAIL_PORT" "MAIL_FROM_ADDRESS")
CONFIG_OK=true

for var in "${REQUIRED_VARS[@]}"; do
    if ! check_env_var "$var"; then
        CONFIG_OK=false
    fi
done

# Check authentication variables if using SMTP
MAIL_MAILER=$(grep "^MAIL_MAILER=" .env | cut -d '=' -f2)
if [ "$MAIL_MAILER" = "smtp" ]; then
    if ! check_env_var "MAIL_USERNAME" || ! check_env_var "MAIL_PASSWORD"; then
        CONFIG_OK=false
    fi
fi

if [ "$CONFIG_OK" = false ]; then
    print_error "Email configuration is incomplete. Please update your .env file."
    exit 1
fi

echo ""
echo "🔧 Setting up Laravel configuration..."

# Clear configuration cache
php artisan config:clear
print_status "Configuration cache cleared"

# Cache configuration for production
php artisan config:cache
print_status "Configuration cached"

# Create queue table if using database queue
QUEUE_CONNECTION=$(grep "^QUEUE_CONNECTION=" .env | cut -d '=' -f2)
if [ "$QUEUE_CONNECTION" = "database" ]; then
    echo ""
    echo "📊 Setting up database queue..."
    
    # Check if queue table exists
    if ! php artisan queue:table --help > /dev/null 2>&1; then
        print_error "Queue table migration not found"
    else
        php artisan queue:table
        php artisan migrate --force
        print_status "Queue table created"
    fi
fi

echo ""
echo "📧 Testing email configuration..."

# Test email configuration
read -p "Enter an email address to test email sending: " TEST_EMAIL

if [ -n "$TEST_EMAIL" ]; then
    echo "Sending test email to $TEST_EMAIL..."
    
    if php artisan email:test "$TEST_EMAIL" --subject="Production Email Test" --message="Your Laravel email configuration is working correctly!"; then
        print_status "Test email sent successfully!"
        echo "Please check $TEST_EMAIL (including spam folder) for the test email."
    else
        print_error "Failed to send test email. Check the logs for details."
        echo "Log file: storage/logs/laravel.log"
    fi
else
    print_warning "Skipping email test"
fi

echo ""
echo "🔒 Security recommendations:"
echo "1. Ensure your .env file has proper permissions (600)"
echo "2. Never commit .env to version control"
echo "3. Set up SPF, DKIM, and DMARC DNS records"
echo "4. Monitor email delivery rates"
echo "5. Set up backup email provider"

# Set proper permissions on .env file
chmod 600 .env
print_status ".env file permissions set to 600"

echo ""
echo "📋 Next steps:"
echo "1. Set up DNS records for your domain"
echo "2. Configure queue workers for production"
echo "3. Set up monitoring for email delivery"
echo "4. Test email functionality thoroughly"

if [ "$QUEUE_CONNECTION" = "database" ] || [ "$QUEUE_CONNECTION" = "redis" ]; then
    echo ""
    print_warning "Don't forget to start queue workers:"
    echo "php artisan queue:work --daemon --tries=3 --timeout=60"
    echo ""
    echo "For production, use a process manager like Supervisor:"
    echo "sudo apt install supervisor"
    echo "Create /etc/supervisor/conf.d/laravel-worker.conf"
fi

echo ""
print_status "Email setup completed!"
echo "Check the documentation at docs/EMAIL_SETUP_GUIDE.md for more details."
