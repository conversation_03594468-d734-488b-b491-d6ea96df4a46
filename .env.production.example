# Laravel Production Environment Configuration
# Copy this file to .env and update with your actual values

APP_NAME="Your App Name"
APP_ENV=production
APP_KEY=base64:your-generated-app-key-here
APP_DEBUG=false
APP_URL=https://yourdomain.com

# Database Configuration
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=your_database_name
DB_USERNAME=your_database_user
DB_PASSWORD=your_secure_database_password

# Email Configuration - Option 1: Pegasus PowerMail
MAIL_MAILER=smtp
MAIL_HOST=smtp.pegasus.powermail.fr
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-secure-email-password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="${APP_NAME}"

# Email Configuration - Option 2: SendGrid
# MAIL_MAILER=smtp
# MAIL_HOST=smtp.sendgrid.net
# MAIL_PORT=587
# MAIL_USERNAME=apikey
# MAIL_PASSWORD=your-sendgrid-api-key
# MAIL_ENCRYPTION=tls
# MAIL_FROM_ADDRESS=<EMAIL>
# MAIL_FROM_NAME="${APP_NAME}"

# Email Configuration - Option 3: Mailgun
# MAIL_MAILER=mailgun
# MAILGUN_DOMAIN=yourdomain.com
# MAILGUN_SECRET=your-mailgun-api-key
# MAILGUN_ENDPOINT=api.mailgun.net
# MAIL_FROM_ADDRESS=<EMAIL>
# MAIL_FROM_NAME="${APP_NAME}"

# Email Configuration - Option 4: Gmail SMTP
# MAIL_MAILER=smtp
# MAIL_HOST=smtp.gmail.com
# MAIL_PORT=587
# MAIL_USERNAME=<EMAIL>
# MAIL_PASSWORD=your-app-password
# MAIL_ENCRYPTION=tls
# MAIL_FROM_ADDRESS=<EMAIL>
# MAIL_FROM_NAME="${APP_NAME}"

# Additional Email Security Settings
MAIL_TIMEOUT=30
MAIL_VERIFY_SSL=true
MAIL_ALLOW_SELF_SIGNED=false
MAIL_EHLO_DOMAIN=yourdomain.com

# Email Rate Limiting
MAIL_RATE_LIMITING=true
MAIL_MAX_PER_MINUTE=60
MAIL_MAX_PER_HOUR=1000

# Queue Configuration (recommended for production)
QUEUE_CONNECTION=database
# or for Redis:
# QUEUE_CONNECTION=redis
# REDIS_HOST=127.0.0.1
# REDIS_PASSWORD=null
# REDIS_PORT=6379

# Cache Configuration
CACHE_DRIVER=redis
# or for file cache:
# CACHE_DRIVER=file

# Session Configuration
SESSION_DRIVER=redis
SESSION_LIFETIME=120

# Logging Configuration
LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=error

# Security Settings
BCRYPT_ROUNDS=12

# PayPal Configuration (if using PayPal)
PAYPAL_MODE=live
PAYPAL_LIVE_CLIENT_ID=your-live-paypal-client-id
PAYPAL_LIVE_CLIENT_SECRET=your-live-paypal-client-secret

# Additional Production Settings
TELESCOPE_ENABLED=false
DEBUGBAR_ENABLED=false
