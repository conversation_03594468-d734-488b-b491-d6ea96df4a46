import { Head, router } from '@inertiajs/react';
import { PageProps, User, BreadcrumbItem, PaginatedData } from '@/types';
import DataTable from '@/components/DataTable';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { useState } from 'react';
import { useForm } from '@inertiajs/react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import AppLayout from '@/layouts/app-layout';
import { format, parseISO } from 'date-fns';
import { fr } from 'date-fns/locale';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { User as UserIcon } from 'lucide-react';

interface ClientsPageProps extends PageProps {
    clients: PaginatedData<User & {
        reservations_count: number;
        reservations_tests_psychos_count: number;
    }>;
}

export default function Index({ clients }: ClientsPageProps) {
    const [isOpen, setIsOpen] = useState(false);
    const [editingClient, setEditingClient] = useState<User | null>(null);
    const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
    const [clientToDelete, setClientToDelete] = useState<User | null>(null);

    const form = useForm({
        civilite: '',
        nom: '',
        prenom: '',
        email: '',
        password: '',
        date_naissance: '',
        lieu_naissance: '',
        ville: '',
        code_postal: '',
        adresse: '',
        mobile: '',
        tel: '',
        num_permis: '',
        date_permis: '',
        lieu_permis: '',
    });

    const columns = [
        {
            key: 'nom_complet',
            label: 'Nom complet',
            render: (value: unknown, row: Record<string, unknown>) => {
                const user = row as unknown as User;
                return (
                    <div className="flex items-center gap-2">
                        <UserIcon className="h-4 w-4 text-muted-foreground" />
                        <span>{user.civilite} {user.prenom} {user.nom}</span>
                    </div>
                );
            }
        },
        { key: 'email', label: 'Email' },
        {
            key: 'mobile',
            label: 'Téléphone',
            render: (value: unknown, row: Record<string, unknown>) => {
                const user = row as unknown as User;
                return user.mobile || 'Non renseigné';
            }
        },
        {
            key: 'reservations_count',
            label: 'Stages',
            render: (value: unknown, row: Record<string, unknown>) => {
                const count = (row as unknown as User & { reservations_count: number }).reservations_count;
                return (
                    <Badge variant={count > 0 ? "default" : "outline"}>
                        {count}
                    </Badge>
                );
            },
        },
        {
            key: 'reservations_tests_psychos_count',
            label: 'Tests Psycho',
            render: (value: unknown, row: Record<string, unknown>) => {
                const count = (row as unknown as User & { reservations_tests_psychos_count: number }).reservations_tests_psychos_count;
                return (
                    <Badge variant={count > 0 ? "default" : "outline"}>
                        {count}
                    </Badge>
                );
            },
        },
        {
            key: 'created_at',
            label: 'Inscription',
            render: (value: unknown, row: Record<string, unknown>) => {
                const date = (row as unknown as User).created_at;
                return date ? format(parseISO(date), 'dd/MM/yyyy', { locale: fr }) : '';
            },
        },
    ];

    const handleAdd = () => {
        setEditingClient(null);
        form.reset();
        setIsOpen(true);
    };

    const handleEdit = (row: Record<string, unknown>) => {
        const client = row as unknown as User;
        setEditingClient(client);
        form.setData({
            civilite: client.civilite || '',
            nom: client.nom || '',
            prenom: client.prenom || '',
            email: client.email || '',
            password: '', // Ne pas remplir le mot de passe pour des raisons de sécurité
            date_naissance: client.date_naissance || '',
            lieu_naissance: client.lieu_naissance || '',
            ville: client.ville || '',
            code_postal: client.code_postal || '',
            adresse: client.adresse || '',
            mobile: client.mobile || '',
            tel: client.tel || '',
            num_permis: client.num_permis || '',
            date_permis: client.date_permis || '',
            lieu_permis: client.lieu_permis || '',
        });
        setIsOpen(true);
    };

    const handleDelete = (row: Record<string, unknown>) => {
        setClientToDelete(row as unknown as User);
        setIsDeleteDialogOpen(true);
    };

    const confirmDelete = () => {
        if (clientToDelete) {
            router.delete(route('admin.clients.destroy', clientToDelete.id), {
                onSuccess: () => {
                    setIsDeleteDialogOpen(false);
                    setClientToDelete(null);
                },
            });
        }
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        if (editingClient) {
            form.put(route('admin.clients.update', editingClient.id), {
                onSuccess: () => {
                    setIsOpen(false);
                    setEditingClient(null);
                },
            });
        } else {
            form.post(route('admin.clients.store'), {
                onSuccess: () => {
                    setIsOpen(false);
                },
            });
        }
    };



    const breadcrumbs: BreadcrumbItem[] = [
        {
            title: 'Clients',
            href: '/admin/clients',
        },
    ];

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Clients" />
            <div className="flex h-full flex-1 flex-col gap-4 rounded-xl p-4">
                <DataTable
                    title="Clients"
                    columns={columns}
                    data={clients.data.map(client => ({
                        id: client.id,
                        civilite: client.civilite,
                        nom: client.nom,
                        prenom: client.prenom,
                        email: client.email,
                        mobile: client.mobile,
                        tel: client.tel,
                        created_at: client.created_at,
                        reservations_count: client.reservations_count,
                        reservations_tests_psychos_count: client.reservations_tests_psychos_count,
                    }))}
                    onAdd={handleAdd}
                    onEdit={handleEdit}
                    onDelete={handleDelete}
                    pagination={{
                        links: clients.links,
                        from: clients.from,
                        to: clients.to,
                        total: clients.total
                    }}
                />
            </div>

            {/* Modal d'ajout/édition de client */}
            <Dialog open={isOpen} onOpenChange={setIsOpen}>
                <DialogContent className="sm:max-w-[600px] md:max-w-[700px] lg:max-w-[900px]">
                    <DialogHeader>
                        <DialogTitle>{editingClient ? 'Modifier le client' : 'Ajouter un client'}</DialogTitle>
                    </DialogHeader>
                    <form onSubmit={handleSubmit} className="space-y-4">
                        <div className="grid grid-cols-2 gap-4">
                            <div className="space-y-2">
                                <Label htmlFor="civilite">Civilité</Label>
                                <Select
                                    value={form.data.civilite}
                                    onValueChange={(value) => form.setData('civilite', value)}
                                >
                                    <SelectTrigger>
                                        <SelectValue placeholder="Sélectionner" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="M.">Monsieur</SelectItem>
                                        <SelectItem value="Mme">Madame</SelectItem>
                                    </SelectContent>
                                </Select>
                                {form.errors.civilite && <p className="text-sm text-destructive">{form.errors.civilite}</p>}
                            </div>
                            <div className="space-y-2">
                                <Label htmlFor="nom">Nom</Label>
                                <Input
                                    id="nom"
                                    value={form.data.nom}
                                    onChange={(e) => form.setData('nom', e.target.value)}
                                    required
                                />
                                {form.errors.nom && <p className="text-sm text-destructive">{form.errors.nom}</p>}
                            </div>
                            <div className="space-y-2">
                                <Label htmlFor="prenom">Prénom</Label>
                                <Input
                                    id="prenom"
                                    value={form.data.prenom}
                                    onChange={(e) => form.setData('prenom', e.target.value)}
                                    required
                                />
                                {form.errors.prenom && <p className="text-sm text-destructive">{form.errors.prenom}</p>}
                            </div>
                            <div className="space-y-2">
                                <Label htmlFor="email">Email</Label>
                                <Input
                                    id="email"
                                    type="email"
                                    value={form.data.email}
                                    onChange={(e) => form.setData('email', e.target.value)}
                                    required
                                />
                                {form.errors.email && <p className="text-sm text-destructive">{form.errors.email}</p>}
                            </div>
                            <div className="space-y-2">
                                <Label htmlFor="password">Mot de passe {editingClient && '(laisser vide pour ne pas modifier)'}</Label>
                                <Input
                                    id="password"
                                    type="password"
                                    value={form.data.password}
                                    onChange={(e) => form.setData('password', e.target.value)}
                                    required={!editingClient}
                                />
                                {form.errors.password && <p className="text-sm text-destructive">{form.errors.password}</p>}
                            </div>
                            <div className="space-y-2">
                                <Label htmlFor="date_naissance">Date de naissance</Label>
                                <Input
                                    id="date_naissance"
                                    type="date"
                                    value={form.data.date_naissance}
                                    onChange={(e) => form.setData('date_naissance', e.target.value)}
                                    className="w-full"
                                />
                                {form.errors.date_naissance && <p className="text-sm text-destructive">{form.errors.date_naissance}</p>}
                            </div>
                        </div>

                        <div className="grid grid-cols-2 gap-4">
                            <div className="space-y-2">
                                <Label htmlFor="lieu_naissance">Lieu de naissance</Label>
                                <Input
                                    id="lieu_naissance"
                                    value={form.data.lieu_naissance}
                                    onChange={(e) => form.setData('lieu_naissance', e.target.value)}
                                />
                                {form.errors.lieu_naissance && <p className="text-sm text-destructive">{form.errors.lieu_naissance}</p>}
                            </div>
                            <div className="space-y-2">
                                <Label htmlFor="adresse">Adresse</Label>
                                <Input
                                    id="adresse"
                                    value={form.data.adresse}
                                    onChange={(e) => form.setData('adresse', e.target.value)}
                                />
                                {form.errors.adresse && <p className="text-sm text-destructive">{form.errors.adresse}</p>}
                            </div>
                            <div className="space-y-2">
                                <Label htmlFor="ville">Ville</Label>
                                <Input
                                    id="ville"
                                    value={form.data.ville}
                                    onChange={(e) => form.setData('ville', e.target.value)}
                                />
                                {form.errors.ville && <p className="text-sm text-destructive">{form.errors.ville}</p>}
                            </div>
                            <div className="space-y-2">
                                <Label htmlFor="code_postal">Code postal</Label>
                                <Input
                                    id="code_postal"
                                    value={form.data.code_postal}
                                    onChange={(e) => form.setData('code_postal', e.target.value)}
                                />
                                {form.errors.code_postal && <p className="text-sm text-destructive">{form.errors.code_postal}</p>}
                            </div>
                        </div>

                        <div className="grid grid-cols-2 gap-4">
                            <div className="space-y-2">
                                <Label htmlFor="mobile">Mobile</Label>
                                <Input
                                    id="mobile"
                                    value={form.data.mobile}
                                    onChange={(e) => form.setData('mobile', e.target.value)}
                                />
                                {form.errors.mobile && <p className="text-sm text-destructive">{form.errors.mobile}</p>}
                            </div>
                            <div className="space-y-2">
                                <Label htmlFor="tel">Téléphone fixe</Label>
                                <Input
                                    id="tel"
                                    value={form.data.tel}
                                    onChange={(e) => form.setData('tel', e.target.value)}
                                />
                                {form.errors.tel && <p className="text-sm text-destructive">{form.errors.tel}</p>}
                            </div>
                        </div>

                        <div className="grid grid-cols-2 gap-4">
                            <div className="space-y-2">
                                <Label htmlFor="num_permis">Numéro de permis</Label>
                                <Input
                                    id="num_permis"
                                    value={form.data.num_permis}
                                    onChange={(e) => form.setData('num_permis', e.target.value)}
                                />
                                {form.errors.num_permis && <p className="text-sm text-destructive">{form.errors.num_permis}</p>}
                            </div>
                            <div className="space-y-2">
                                <Label htmlFor="date_permis">Date du permis</Label>
                                <Input
                                    id="date_permis"
                                    type="date"
                                    value={form.data.date_permis}
                                    onChange={(e) => form.setData('date_permis', e.target.value)}
                                    className="w-full"
                                />
                                {form.errors.date_permis && <p className="text-sm text-destructive">{form.errors.date_permis}</p>}
                            </div>
                            <div className="space-y-2">
                                <Label htmlFor="lieu_permis">Lieu du permis</Label>
                                <Input
                                    id="lieu_permis"
                                    value={form.data.lieu_permis}
                                    onChange={(e) => form.setData('lieu_permis', e.target.value)}
                                />
                                {form.errors.lieu_permis && <p className="text-sm text-destructive">{form.errors.lieu_permis}</p>}
                            </div>
                        </div>

                        <div className="flex justify-end gap-2">
                            <Button type="button" variant="outline" onClick={() => setIsOpen(false)}>
                                Annuler
                            </Button>
                            <Button type="submit" disabled={form.processing}>
                                {editingClient ? 'Mettre à jour' : 'Ajouter'}
                            </Button>
                        </div>
                    </form>
                </DialogContent>
            </Dialog>

            {/* Modal de confirmation de suppression */}
            <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
                <DialogContent >
                    <DialogHeader>
                        <DialogTitle>Confirmer la suppression</DialogTitle>
                    </DialogHeader>
                    <p>Êtes-vous sûr de vouloir supprimer ce client ? Cette action est irréversible.</p>
                    <div className="flex justify-end gap-2">
                        <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
                            Annuler
                        </Button>
                        <Button variant="destructive" onClick={confirmDelete}>
                            Supprimer
                        </Button>
                    </div>
                </DialogContent>
            </Dialog>
        </AppLayout>
    );
}
