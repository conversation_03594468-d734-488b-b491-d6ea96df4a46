<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Ville extends Model
{
    use HasFactory;

    protected $fillable = ['nom', 'departement_id', 'code', 'reg_code', 'dep_code', 'code_postal'];

    public function departement()
    {
        return $this->belongsTo(Departement::class);
    }

    public function lieus()
    {
        return $this->hasMany(Lieu::class);
    }
}
