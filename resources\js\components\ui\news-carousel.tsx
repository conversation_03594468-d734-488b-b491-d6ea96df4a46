"use client";

import { useEffect, useState } from "react";
import {
  Carousel,
  CarouselApi,
  CarouselContent,
  CarouselItem,
} from "@/components/ui/carousel";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { ArrowRight, Newspaper } from "lucide-react";
import { Link } from "@inertiajs/react";

interface NewsItem {
  title: string;
  content: string;
  link: string;
}

const newsItems: NewsItem[] = [
  {
    title: "SPÉCIAL SÉCURITÉ ROUTIÈRE avec la Fondation VINCI Autoroutes",
    content: "POUR UN CERVEAU RESPONSABLE AU VOLANT\nEnfin les vacances ! Pour les uns, les doux rivages de la mer, pour d'autres, la pureté des sommets ou les vertes prairies. Mais pour beaucoup, un voyage en voiture – soit quelque 30 millions de déplacements sur les routes. Comment rester serein et vigilant au cours de cette période encombrée, parfois fatigante, voire stressante ?",
    link: "https://www.cerveauetpsycho.fr/download/article?id_element=14380"
  },
  {
    title: "Démenti de la Délégation à la Sécurité routière",
    content: "02 juillet 2017\nHier, des informations erronées sur une prétendue évolution des règles du code de la route au 1er juillet ont été reprises par certains médias. La DSR tient à apporter les précisions suivantes : La seule modification intervenue le 1er juillet 2017 porte sur la taille réglementaire des plaques de deux et trois-roues motorisés, comme l'a annoncé la Délégation à la Sécurité routière par voie de communiqué de presse.",
    link: "http://www.securite-routiere.gouv.fr/medias/espace-presse/publications-presse/dementi-de-la-delegation-a-la-securite-routiere"
  },
  {
    title: "À partir du 31 décembre 2016, onze catégories d'infractions routières seront verbalisables...",
    content: "Afin d'intensifier la lutte contre les comportements dangereux, les mesures 3 et 6 du Comité interministériel de la sécurité routière (CISR) du 2 octobre 2015 ont prévu d'étendre le nombre des infractions pouvant être constatées, sans interception en bord de route, par l'intermédiaire de la vidéo-verbalisation et des radars homologués. La procédure de vidéo-verbalisation des infractions routières existe depuis 2008. Elle permet à un agent assermenté de constater sur un écran de contrôle une infraction...",
    link: "http://www.securite-routiere.gouv.fr/medias/espace-presse/publications-presse/a-partir-du-31-decembre-2016-onze-categories-d-infractions-routieres-seront-verbalisables-sans-interception-du-conducteur"
  }
];

export function NewsCarousel() {
  const [api, setApi] = useState<CarouselApi>();
  const [current, setCurrent] = useState(0);

  useEffect(() => {
    if (!api) {
      return;
    }

    const interval = setInterval(() => {
      if (api.selectedScrollSnap() + 1 === api.scrollSnapList().length) {
        setCurrent(0);
        api.scrollTo(0);
      } else {
        api.scrollNext();
        setCurrent(current + 1);
      }
    }, 6000);

    return () => clearInterval(interval);
  }, [api, current]);

  return (
    <div className="flex flex-col h-full">
        <h2 className="text-primary dark:text-primary mb-4 flex items-center text-xl font-semibold">
              <Newspaper className="mr-2 h-5 w-5" /> Les actualités
            </h2>
      <Carousel
        setApi={setApi}
        className="w-full flex-1"
        opts={{
          axis: "y",
          loop: true,
        }}
        orientation="vertical"
      >
        <CarouselContent className="-mt-2 h-[600px]">
          {newsItems.map((item, index) => (
            <CarouselItem key={index} className="pt-2 basis-1/2">
              <Card className="h-full">
                <CardHeader className="bg-primary text-white py-3">
                  <CardTitle className="text-center uppercase text-sm">
                    {item.title}
                  </CardTitle>
                </CardHeader>
                <CardContent className="p-6 flex flex-col h-[calc(100%-3rem)]">
                  <p className="mb-2 text-md font-thin flex-1 overflow-hidden line-clamp-[6]">
                    {item.content.split('\n').map((text, i) => (
                      <span key={i} className="block">
                        {i === 0 ? <strong>{text}</strong> : text}
                      </span>
                    ))}
                  </p>
                  <div className="flex justify-end mt-auto">
                    <Button variant="link" asChild className="text-primary p-0">
                      <Link href={item.link} target="_blank" className="flex items-center gap-1">
                        En savoir plus <ArrowRight className="h-4 w-4" />
                      </Link>
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </CarouselItem>
          ))}
        </CarouselContent>
      </Carousel>
    </div>
  );
}
