<?php

namespace Database\Seeders;

use App\Models\TypeTestPsycho;
use Illuminate\Database\Seeder;

class TypeTestPsychoSeeder extends Seeder
{
    public function run(): void
    {
        $types = [
            [
                'nom' => 'Invalidation décision administrative (Solde de Points Nul )',
                'description' => 'Invalidation décision administrative (Solde de Points Nul )'
            ],
            [
                'nom' => 'Annulation ( Décision Judiciaire )',
                'description' => 'Annulation ( Décision Judiciaire )'
            ],
            [
                'nom' => 'Suspension',
                'description' => 'Suspension'
            ],
        ];

        foreach ($types as $type) {
            TypeTestPsycho::create($type);
        }
    }
}
