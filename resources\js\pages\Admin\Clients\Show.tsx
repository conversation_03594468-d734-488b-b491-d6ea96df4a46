import { <PERSON>, <PERSON> } from '@inertiajs/react';
import { PageProps, User, Reservation, ReservationTestPsycho, BreadcrumbItem } from '@/types';
import AppLayout from '@/layouts/app-layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { format, parseISO } from 'date-fns';
import { fr } from 'date-fns/locale';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { ArrowLeft, Calendar, MapPin, Phone, Mail, User as UserIcon, CreditCard, FileText } from 'lucide-react';

interface ClientPageProps extends PageProps {
    client: User & {
        reservations: Reservation[];
        reservations_tests_psychos: ReservationTestPsycho[];
    };
}

export default function Show({ client }: ClientPageProps) {
    const breadcrumbs: BreadcrumbItem[] = [
        {
            title: 'Clients',
            href: '/admin/clients',
        },
        {
            title: `${client.prenom} ${client.nom}`,
            href: `/admin/clients/${client.id}`,
        },
    ];

    // Fonction pour formater les dates
    const formatDate = (dateString: string | null) => {
        if (!dateString) return 'Non renseigné';
        return format(parseISO(dateString), 'dd/MM/yyyy', { locale: fr });
    };

    // Fonction pour obtenir la couleur du badge en fonction du statut
    const getStatusColor = (status: string) => {
        switch (status.toLowerCase()) {
            case 'confirmée':
                return 'success';
            case 'en attente':
                return 'warning';
            case 'annulée':
                return 'destructive';
            default:
                return 'secondary';
        }
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title={`Client: ${client.prenom} ${client.nom}`} />
            <div className="flex h-full flex-1 flex-col gap-4 rounded-xl p-4">
                <div className="flex justify-between items-center">
                    <Button variant="outline" asChild>
                        <Link href={route('admin.clients.index')}>
                            <ArrowLeft className="mr-2 h-4 w-4" />
                            Retour à la liste
                        </Link>
                    </Button>
                    <Button asChild>
                        <Link href={route('admin.clients.edit', client.id)}>
                            Modifier
                        </Link>
                    </Button>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    {/* Informations personnelles */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Informations personnelles</CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="flex items-center gap-2">
                                <UserIcon className="h-4 w-4 text-muted-foreground" />
                                <span className="font-medium">{client.civilite} {client.prenom} {client.nom}</span>
                            </div>
                            <div className="flex items-center gap-2">
                                <Mail className="h-4 w-4 text-muted-foreground" />
                                <span>{client.email}</span>
                            </div>
                            <div className="flex items-center gap-2">
                                <Phone className="h-4 w-4 text-muted-foreground" />
                                <span>{client.mobile || 'Non renseigné'}</span>
                            </div>
                            <div className="flex items-center gap-2">
                                <Calendar className="h-4 w-4 text-muted-foreground" />
                                <span>Né(e) le {formatDate(client.date_naissance)}</span>
                            </div>
                            <div className="flex items-center gap-2">
                                <MapPin className="h-4 w-4 text-muted-foreground" />
                                <span>{client.lieu_naissance || 'Non renseigné'}</span>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Coordonnées */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Coordonnées</CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="flex items-center gap-2">
                                <MapPin className="h-4 w-4 text-muted-foreground" />
                                <span>{client.adresse || 'Non renseigné'}</span>
                            </div>
                            <div className="flex items-center gap-2">
                                <MapPin className="h-4 w-4 text-muted-foreground" />
                                <span>{client.code_postal} {client.ville}</span>
                            </div>
                            <div className="flex items-center gap-2">
                                <Phone className="h-4 w-4 text-muted-foreground" />
                                <span>Fixe: {client.tel || 'Non renseigné'}</span>
                            </div>
                            <div className="flex items-center gap-2">
                                <Calendar className="h-4 w-4 text-muted-foreground" />
                                <span>Inscrit le {formatDate(client.created_at)}</span>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Informations permis */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Informations permis</CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="flex items-center gap-2">
                                <CreditCard className="h-4 w-4 text-muted-foreground" />
                                <span>N° {client.num_permis || 'Non renseigné'}</span>
                            </div>
                            <div className="flex items-center gap-2">
                                <Calendar className="h-4 w-4 text-muted-foreground" />
                                <span>Délivré le {formatDate(client.date_permis)}</span>
                            </div>
                            <div className="flex items-center gap-2">
                                <MapPin className="h-4 w-4 text-muted-foreground" />
                                <span>à {client.lieu_permis || 'Non renseigné'}</span>
                            </div>
                        </CardContent>
                    </Card>
                </div>

                {/* Historique des réservations */}
                <Card className="mt-4">
                    <CardHeader>
                        <CardTitle>Historique des réservations</CardTitle>
                        <CardDescription>
                            Toutes les réservations de stages et tests psychotechniques du client
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <Tabs defaultValue="stages">
                            <TabsList className="mb-4">
                                <TabsTrigger value="stages">
                                    Stages ({client.reservations.length})
                                </TabsTrigger>
                                <TabsTrigger value="tests">
                                    Tests Psychotechniques ({client.reservations_tests_psychos.length})
                                </TabsTrigger>
                            </TabsList>
                            <TabsContent value="stages">
                                {client.reservations.length > 0 ? (
                                    <Table>
                                        <TableHeader>
                                            <TableRow>
                                                <TableHead>Date</TableHead>
                                                <TableHead>Lieu</TableHead>
                                                <TableHead>Type</TableHead>
                                                <TableHead>Statut</TableHead>
                                                <TableHead>Paiement</TableHead>
                                                <TableHead>Actions</TableHead>
                                            </TableRow>
                                        </TableHeader>
                                        <TableBody>
                                            {client.reservations.map((reservation) => (
                                                <TableRow key={reservation.id}>
                                                    <TableCell>
                                                        {reservation.stage && formatDate(reservation.stage.date_debut)}
                                                    </TableCell>
                                                    <TableCell>
                                                        {reservation.stage?.lieu?.nom}, {reservation.stage?.lieu?.ville?.nom}
                                                    </TableCell>
                                                    <TableCell>
                                                        {reservation.type_stage?.nom}
                                                    </TableCell>
                                                    <TableCell>
                                                        <Badge variant={getStatusColor(reservation.statut) as any}>
                                                            {reservation.statut}
                                                        </Badge>
                                                    </TableCell>
                                                    <TableCell>
                                                        {reservation.methode_paiement ? (
                                                            <div className="flex flex-col">
                                                                <span>{reservation.methode_paiement}</span>
                                                                <span className="text-xs text-muted-foreground">
                                                                    {reservation.date_paiement && formatDate(reservation.date_paiement)}
                                                                </span>
                                                            </div>
                                                        ) : (
                                                            <Badge variant="outline">Non payé</Badge>
                                                        )}
                                                    </TableCell>
                                                    <TableCell>
                                                        <Button variant="outline" size="sm" asChild>
                                                            <Link href={route('admin.reservations.show', reservation.id)}>
                                                                <FileText className="mr-2 h-4 w-4" />
                                                                Détails
                                                            </Link>
                                                        </Button>
                                                    </TableCell>
                                                </TableRow>
                                            ))}
                                        </TableBody>
                                    </Table>
                                ) : (
                                    <div className="text-center py-8 text-muted-foreground">
                                        Aucune réservation de stage pour ce client.
                                    </div>
                                )}
                            </TabsContent>
                            <TabsContent value="tests">
                                {client.reservations_tests_psychos.length > 0 ? (
                                    <Table>
                                        <TableHeader>
                                            <TableRow>
                                                <TableHead>Date</TableHead>
                                                <TableHead>Lieu</TableHead>
                                                <TableHead>Type</TableHead>
                                                <TableHead>Statut</TableHead>
                                                <TableHead>Paiement</TableHead>
                                                <TableHead>Actions</TableHead>
                                            </TableRow>
                                        </TableHeader>
                                        <TableBody>
                                            {client.reservations_tests_psychos.map((reservation) => (
                                                <TableRow key={reservation.id}>
                                                    <TableCell>
                                                        {reservation.test_psycho && formatDate(reservation.test_psycho.date)}
                                                    </TableCell>
                                                    <TableCell>
                                                        {reservation.test_psycho?.lieu?.nom}, {reservation.test_psycho?.lieu?.ville?.nom}
                                                    </TableCell>
                                                    <TableCell>
                                                        {reservation.type_test_psycho?.nom}
                                                    </TableCell>
                                                    <TableCell>
                                                        <Badge variant={getStatusColor(reservation.statut) as any}>
                                                            {reservation.statut}
                                                        </Badge>
                                                    </TableCell>
                                                    <TableCell>
                                                        {reservation.methode_paiement ? (
                                                            <div className="flex flex-col">
                                                                <span>{reservation.methode_paiement}</span>
                                                                <span className="text-xs text-muted-foreground">
                                                                    {reservation.date_paiement && formatDate(reservation.date_paiement)}
                                                                </span>
                                                            </div>
                                                        ) : (
                                                            <Badge variant="outline">Non payé</Badge>
                                                        )}
                                                    </TableCell>
                                                    <TableCell>
                                                        <Button variant="outline" size="sm" asChild>
                                                            <Link href={route('admin.reservations-tests-psychos.show', reservation.id)}>
                                                                <FileText className="mr-2 h-4 w-4" />
                                                                Détails
                                                            </Link>
                                                        </Button>
                                                    </TableCell>
                                                </TableRow>
                                            ))}
                                        </TableBody>
                                    </Table>
                                ) : (
                                    <div className="text-center py-8 text-muted-foreground">
                                        Aucune réservation de test psychotechnique pour ce client.
                                    </div>
                                )}
                            </TabsContent>
                        </Tabs>
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
