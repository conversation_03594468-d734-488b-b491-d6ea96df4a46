import * as React from "react"
import { Area, AreaChart, CartesianGrid, XAxis, Tooltip, Responsive<PERSON><PERSON><PERSON>, <PERSON> } from "recharts"

import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { TrendingDown, TrendingUp } from "lucide-react";

interface StageStatusChartProps {
  data: {
    date: string;
    confirmée: number;
    "en attente": number;
    annulée: number;
  }[];
  trend: {
    percentage: number;
    isUp: boolean;
  };
}

export function StageStatusChart({ data, trend }: StageStatusChartProps) {
  const [timeRange, setTimeRange] = React.useState("90d")

  // Vérifier si les données sont valides
  const hasValidData = Array.isArray(data) && data.length > 0;

  console.log("Stage Chart Data:", data);
  console.log("Stage Chart Trend:", trend);

  // Filtrer les données en fonction de la plage de temps sélectionnée
  const filteredData = React.useMemo(() => {
    if (!hasValidData) return [];

    const referenceDate = new Date();
    let daysToSubtract = 90;

    if (timeRange === "30d") {
      daysToSubtract = 30;
    } else if (timeRange === "7d") {
      daysToSubtract = 7;
    }

    const startDate = new Date(referenceDate);
    startDate.setDate(startDate.getDate() - daysToSubtract);

    return data.filter(item => {
      const date = new Date(item.date);
      return date >= startDate;
    });
  }, [data, timeRange, hasValidData]);

  return (
    <Card>
      <CardHeader className="flex items-center gap-2 space-y-0 border-b py-5 sm:flex-row">
        <div className="grid flex-1 gap-1 text-center sm:text-left">
          <CardTitle>Réservations de Stages</CardTitle>
          <CardDescription>
            Évolution des réservations par statut
          </CardDescription>
        </div>
        <Select value={timeRange} onValueChange={setTimeRange}>
          <SelectTrigger
            className="w-[160px] rounded-lg sm:ml-auto"
            aria-label="Sélectionner une période"
          >
            <SelectValue placeholder="90 derniers jours" />
          </SelectTrigger>
          <SelectContent className="rounded-xl">
            <SelectItem value="90d" className="rounded-lg">
              90 derniers jours
            </SelectItem>
            <SelectItem value="30d" className="rounded-lg">
              30 derniers jours
            </SelectItem>
            <SelectItem value="7d" className="rounded-lg">
              7 derniers jours
            </SelectItem>
          </SelectContent>
        </Select>
      </CardHeader>
      <CardContent className="px-2 pt-4 sm:px-6 sm:pt-6">
        {!hasValidData ? (
          <div className="flex h-[250px] items-center justify-center text-muted-foreground">
            Aucune donnée disponible pour le graphique
          </div>
        ) : (
          <div className="h-[250px] w-full">
            <ResponsiveContainer width="100%" height="100%">
              <AreaChart data={filteredData}>
                <defs>
                  <linearGradient id="fillConfirmée" x1="0" y1="0" x2="0" y2="1">
                    <stop
                      offset="5%"
                      stopColor="var(--chart-1)"
                      stopOpacity={0.8}
                    />
                    <stop
                      offset="95%"
                      stopColor="var(--chart-1)"
                      stopOpacity={0.1}
                    />
                  </linearGradient>
                  <linearGradient id="fillEnAttente" x1="0" y1="0" x2="0" y2="1">
                    <stop
                      offset="5%"
                      stopColor="var(--chart-2)"
                      stopOpacity={0.8}
                    />
                    <stop
                      offset="95%"
                      stopColor="var(--chart-2)"
                      stopOpacity={0.1}
                    />
                  </linearGradient>
                  <linearGradient id="fillAnnulée" x1="0" y1="0" x2="0" y2="1">
                    <stop
                      offset="5%"
                      stopColor="var(--chart-3)"
                      stopOpacity={0.8}
                    />
                    <stop
                      offset="95%"
                      stopColor="var(--chart-3)"
                      stopOpacity={0.1}
                    />
                  </linearGradient>
                </defs>
                <CartesianGrid vertical={false} strokeDasharray="3 3" />
                <XAxis
                  dataKey="date"
                  tickLine={false}
                  axisLine={false}
                  tickMargin={8}
                  minTickGap={32}
                  tickFormatter={(value) => {
                    const date = new Date(value);
                    return date.toLocaleDateString("fr-FR", {
                      month: "short",
                      day: "numeric",
                    });
                  }}
                />
                <Tooltip
                  contentStyle={{
                    backgroundColor: 'var(--background)',
                    border: '1px solid var(--border)',
                    borderRadius: '0.5rem',
                    padding: '0.5rem'
                  }}
                  formatter={(value,name) => [`${value}`, `${name}`]}
                  labelFormatter={(value) => {
                    const date = new Date(value);
                    return date.toLocaleDateString("fr-FR", {
                      year: "numeric",
                      month: "long",
                      day: "numeric",
                    });
                  }}
                />
                <Legend />
                <Area
                  name="Confirmées"
                  dataKey="confirmée"
                  type="monotone"
                  fill="url(#fillConfirmée)"
                  stroke="var(--chart-1)"
                  strokeWidth={2}
                  stackId="1"
                />
                <Area
                  name="En attente"
                  dataKey="en attente"
                  type="monotone"
                  fill="url(#fillEnAttente)"
                  stroke="var(--chart-2)"
                  strokeWidth={2}
                  stackId="2"
                />
                <Area
                  name="Annulées"
                  dataKey="annulée"
                  type="monotone"
                  fill="url(#fillAnnulée)"
                  stroke="var(--chart-3)"
                  strokeWidth={2}
                  stackId="3"
                />
              </AreaChart>
            </ResponsiveContainer>
          </div>
        )}
      </CardContent>
      <CardFooter className="flex-col items-start gap-2 text-sm">
        <div className="flex gap-2 font-medium leading-none">
          {trend.isUp ? (
            <>
              En hausse de {trend.percentage}% ce mois-ci <TrendingUp className="h-4 w-4 text-green-500" />
            </>
          ) : (
            <>
              En baisse de {trend.percentage}% ce mois-ci <TrendingDown className="h-4 w-4 text-red-500" />
            </>
          )}
        </div>
        <div className="leading-none text-muted-foreground">
          Évolution des réservations de stages pour les 6 derniers mois
        </div>
      </CardFooter>
    </Card>
  )
}
