<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('villes', function (Blueprint $table) {
            $table->string('code')->after('nom')->nullable();
            $table->string('reg_code')->after('code')->nullable();
            $table->string('dep_code')->after('reg_code')->nullable();
            $table->string('code_postal')->after('dep_code')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('villes', function (Blueprint $table) {
            $table->dropColumn(['code', 'reg_code', 'dep_code', 'code_postal']);
        });
    }
};
