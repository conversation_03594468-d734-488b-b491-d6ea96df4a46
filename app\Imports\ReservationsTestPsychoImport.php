<?php

namespace App\Imports;

use App\Models\ReservationTestPsycho;
use App\Models\User;
use App\Models\TestPsycho;
use App\Models\TypeTestPsycho;
use Carbon\Carbon;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithValidation;
use Maatwebsite\Excel\Concerns\Importable;
use Maatwebsite\Excel\Concerns\SkipsOnError;
use Maatwebsite\Excel\Concerns\SkipsErrors;
use Maatwebsite\Excel\Concerns\WithBatchInserts;
use Maatwebsite\Excel\Concerns\WithChunkReading;

class ReservationsTestPsychoImport implements ToModel, WithHeadingRow, WithValidation, SkipsOnError, WithBatchInserts, WithChunkReading
{
    use Importable, SkipsErrors;

    private $newUsersCount = 0;
    private $existingUsersCount = 0;
    private $reservationsCount = 0;
    private $generatedPasswords = [];

    /**
     * @param array $row
     *
     * @return \Illuminate\Database\Eloquent\Model|null
     */
    public function model(array $row)
    {
        try {
            // Vérifier si l'utilisateur existe déjà
            $user = User::where('email', $row['email'])->first();
            
            // Si l'utilisateur n'existe pas, le créer
            if (!$user) {
                // Générer un mot de passe aléatoire si non fourni
                $password = $row['password'] ?? Str::random(10);
                
                // Convertir la date de naissance
                $date_naissance = null;
                if (isset($row['date_naissance'])) {
                    try {
                        $date_naissance = $this->parseDate($row['date_naissance']);
                    } catch (\Exception $e) {
                        Log::warning("Erreur lors de la conversion de la date de naissance: " . $e->getMessage());
                    }
                }
                
                // Convertir la date du permis
                $date_permis = null;
                if (isset($row['date_permis'])) {
                    try {
                        $date_permis = $this->parseDate($row['date_permis']);
                    } catch (\Exception $e) {
                        Log::warning("Erreur lors de la conversion de la date du permis: " . $e->getMessage());
                    }
                }
                
                // Créer l'utilisateur
                $user = User::create([
                    'nom' => $row['nom'],
                    'prenom' => $row['prenom'],
                    'email' => $row['email'],
                    'password' => Hash::make($password),
                    'role' => 'client',
                    'civilite' => $row['civilite'] ?? null,
                    'date_naissance' => $date_naissance,
                    'lieu_naissance' => $row['lieu_naissance'] ?? null,
                    'ville' => $row['ville'] ?? null,
                    'code_postal' => $row['cp'] ?? null,
                    'adresse' => $row['adresse'] ?? null,
                    'mobile' => $row['mobile'] ?? null,
                    'tel' => $row['tel'] ?? null,
                    'num_permis' => $row['num_permis'] ?? null,
                    'date_permis' => $date_permis,
                    'lieu_permis' => $row['lieu_permis'] ?? null,
                ]);
                
                // Stocker le mot de passe généré pour l'afficher plus tard
                $this->generatedPasswords[$user->email] = $password;
                $this->newUsersCount++;
            } else {
                $this->existingUsersCount++;
            }
            
            // Convertir la date de réservation
            $date_reservation = null;
            if (isset($row['date_reservation'])) {
                try {
                    $date_reservation = $this->parseDate($row['date_reservation']);
                } catch (\Exception $e) {
                    Log::warning("Erreur lors de la conversion de la date de réservation: " . $e->getMessage());
                    $date_reservation = now();
                }
            } else {
                $date_reservation = now();
            }
            
            // Créer la réservation
            $reservation = new ReservationTestPsycho([
                'test_psycho_id' => $row['test_id'],
                'user_id' => $user->id,
                'type_test_psycho_id' => $row['type_test_id'],
                'date_reservation' => $date_reservation,
                'statut' => $row['statut'] ?? 'en attente',
                'methode_paiement' => $row['methode_paiement'] ?? null,
            ]);
            
            $this->reservationsCount++;
            
            return $reservation;
        } catch (\Exception $e) {
            // Log l'erreur
            Log::error('Erreur lors de l\'importation d\'une réservation de test psychotechnique: ' . $e->getMessage(), $row);
            return null;
        }
    }
    
    /**
     * Parse a date from various formats
     */
    private function parseDate($dateValue)
    {
        // Vérifier si c'est un nombre (format Excel)
        if (is_numeric($dateValue)) {
            // Convertir le nombre Excel en date
            return Carbon::instance(\PhpOffice\PhpSpreadsheet\Shared\Date::excelToDateTimeObject((int)$dateValue));
        } else {
            // Essayer différents formats de date textuels
            $dateFormats = ['d/m/Y', 'd-m-Y', 'Y-m-d'];
            
            foreach ($dateFormats as $format) {
                try {
                    return Carbon::createFromFormat($format, $dateValue);
                } catch (\Exception $e) {
                    continue;
                }
            }
        }
        
        throw new \Exception("Format de date non reconnu: {$dateValue}");
    }

    /**
     * @return array
     */
    public function rules(): array
    {
        return [
            'nom' => 'required|string|max:255',
            'prenom' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'test_id' => 'required|exists:test_psychos,id',
            'type_test_id' => 'required|exists:type_test_psychos,id',
            'statut' => 'sometimes|in:confirmée,en attente,annulée',
            'methode_paiement' => 'sometimes|string|max:255',
        ];
    }

    /**
     * @return array
     */
    public function customValidationMessages()
    {
        return [
            'nom.required' => 'Le nom est obligatoire',
            'prenom.required' => 'Le prénom est obligatoire',
            'email.required' => 'L\'email est obligatoire',
            'email.email' => 'L\'email doit être une adresse email valide',
            'test_id.required' => 'L\'ID du test est obligatoire',
            'test_id.exists' => 'Le test sélectionné n\'existe pas',
            'type_test_id.required' => 'Le type de test est obligatoire',
            'type_test_id.exists' => 'Le type de test sélectionné n\'existe pas',
            'statut.in' => 'Le statut doit être l\'un des suivants: confirmée, en attente, annulée',
        ];
    }
    
    /**
     * @return int
     */
    public function batchSize(): int
    {
        return 100;
    }
    
    /**
     * @return int
     */
    public function chunkSize(): int
    {
        return 100;
    }
    
    /**
     * Get the number of new users created
     */
    public function getNewUsersCount(): int
    {
        return $this->newUsersCount;
    }
    
    /**
     * Get the number of existing users found
     */
    public function getExistingUsersCount(): int
    {
        return $this->existingUsersCount;
    }
    
    /**
     * Get the number of reservations created
     */
    public function getReservationsCount(): int
    {
        return $this->reservationsCount;
    }
    
    /**
     * Get the generated passwords for new users
     */
    public function getGeneratedPasswords(): array
    {
        return $this->generatedPasswords;
    }
}
