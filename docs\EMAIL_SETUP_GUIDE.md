# Laravel Email Configuration Guide for Production

## 4. Testing and Troubleshooting

### Testing Email Functionality

#### 1. Command Line Testing
```bash
# Test email configuration
php artisan email:test <EMAIL>

# Test with custom subject and message
php artisan email:test <EMAIL> --subject="Production Test" --message="Testing production email setup"
```

#### 2. Browser Testing
Create a test route in `routes/web.php`:
```php
Route::get('/test-email', function () {
    try {
        Mail::raw('Test email from Laravel production', function ($message) {
            $message->to('<EMAIL>')
                   ->subject('Production Email Test');
        });
        return 'Email sent successfully!';
    } catch (Exception $e) {
        return 'Email failed: ' . $e->getMessage();
    }
})->middleware('auth'); // Protect with authentication
```

#### 3. Queue Testing (if using queues)
```bash
# Test queued email
php artisan queue:work --once

# Check failed jobs
php artisan queue:failed

# Retry failed jobs
php artisan queue:retry all
```

### Common Issues and Solutions

#### Issue 1: "Connection could not be established with host"
**Symptoms:** SMTP connection fails
**Solutions:**
- Check firewall settings (ports 25, 465, 587, 2525)
- Verify MAIL_HOST is correct
- Try different ports (587 for TLS, 465 for SSL)
- Check if your hosting provider blocks SMTP

#### Issue 2: "Authentication failed"
**Symptoms:** SMTP authentication errors
**Solutions:**
- Verify MAIL_USERNAME and MAIL_PASSWORD
- For Gmail: Use App Passwords instead of regular password
- Check if 2FA is enabled and requires app-specific passwords
- Ensure credentials are properly escaped in .env file

#### Issue 3: "SSL certificate problem"
**Symptoms:** SSL/TLS verification errors
**Solutions:**
```env
# Temporarily disable SSL verification (NOT recommended for production)
MAIL_VERIFY_SSL=false

# Or update your server's CA certificates
# sudo apt-get update && sudo apt-get install ca-certificates
```

#### Issue 4: Emails going to spam
**Solutions:**
- Set up SPF record: `v=spf1 include:_spf.google.com ~all`
- Set up DKIM signing
- Set up DMARC policy
- Use a dedicated IP address
- Maintain good sender reputation

### Debugging Commands

```bash
# Check mail configuration
php artisan config:show mail

# Clear configuration cache
php artisan config:clear

# Check logs
tail -f storage/logs/laravel.log

# Test SMTP connection manually
telnet smtp.pegasus.powermail.fr 587
```

## 5. Laravel-Specific Configuration

### Queue Configuration for Emails

#### Update `.env` for queue processing:
```env
QUEUE_CONNECTION=database
# or
QUEUE_CONNECTION=redis
```

#### Create queue table:
```bash
php artisan queue:table
php artisan migrate
```

#### Use queued emails:
```php
// In your controller or service
Mail::to($user->email)->queue(new WelcomeEmail($user));
```

#### Start queue worker:
```bash
# For production, use supervisor or similar process manager
php artisan queue:work --daemon --tries=3 --timeout=60
```

### Mail Notification Setup

#### Create notification:
```bash
php artisan make:notification UserRegistered
```

#### Example notification:
```php
<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class UserRegistered extends Notification implements ShouldQueue
{
    use Queueable;

    public function via($notifiable)
    {
        return ['mail'];
    }

    public function toMail($notifiable)
    {
        return (new MailMessage)
                    ->subject('Welcome to ' . config('app.name'))
                    ->greeting('Hello ' . $notifiable->name . '!')
                    ->line('Welcome to our application.')
                    ->action('Get Started', url('/dashboard'))
                    ->line('Thank you for using our application!');
    }
}
```

### Advanced Configuration

#### Custom Mail Driver
```php
// In AppServiceProvider::boot()
Mail::extend('custom', function (array $config) {
    return new CustomTransport($config);
});
```

#### Mail Event Listeners
```php
// In EventServiceProvider::boot()
Mail::sending(function ($message) {
    // Log all outgoing emails
    Log::info('Sending email', [
        'to' => $message->getTo(),
        'subject' => $message->getSubject()
    ]);
});
```

## Production Deployment Checklist

- [ ] Environment variables properly set
- [ ] SSL/TLS encryption enabled
- [ ] Rate limiting configured
- [ ] Queue workers running (if using queues)
- [ ] DNS records configured (SPF, DKIM, DMARC)
- [ ] Email templates tested
- [ ] Error logging enabled
- [ ] Backup email provider configured
- [ ] Monitoring alerts set up
- [ ] Test emails sent and received

## Security Best Practices

1. **Never commit credentials to version control**
2. **Use environment variables for all sensitive data**
3. **Enable SSL/TLS encryption**
4. **Implement rate limiting**
5. **Validate email addresses**
6. **Log email activities**
7. **Use queues for bulk emails**
8. **Set up proper DNS records**
9. **Monitor email delivery rates**
10. **Have a backup email provider**
