<?php

namespace Database\Factories;

use App\Models\Stage;
use App\Models\TypeStage;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

class ReservationFactory extends Factory
{
    public function definition(): array
    {
        return [
            'stage_id' => Stage::factory(),
            'user_id' => User::factory(),
            'type_stage_id' => TypeStage::factory(),
            'date_reservation' => fake()->dateTimeBetween('-1 month', 'now'),
            'statut' => fake()->randomElement(['confirmée', 'en attente', 'annulée']),
            'date_infraction' => fake()->dateTimeBetween('-6 months', '-1 month'),
            'heure_infraction' => fake()->time(),
            'lieu_infraction' => fake()->address(),
            'permis_recto' => 'permis/recto/' . fake()->uuid() . '.pdf',
            'permis_verso' => 'permis/verso/' . fake()->uuid() . '.pdf',
            'lettre_48n_recto' => 'lettre48n/recto/' . fake()->uuid() . '.pdf',
            'lettre_48n_verso' => 'lettre48n/verso/' . fake()->uuid() . '.pdf',
        ];
    }
}