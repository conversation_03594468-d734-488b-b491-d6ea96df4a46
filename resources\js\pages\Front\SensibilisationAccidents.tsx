import FrontLayout from '@/layouts/front-layout';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { HeroSection } from '@/components/hero-section-dark';
import { Clock } from 'lucide-react';

export default function SensibilisationAccidents() {
  return (
    <FrontLayout title="Programme du stage">
      <HeroSection
        subtitle={{
          regular: "PROGRAMME DU STAGE : ",
          gradient: "Stage de sensibilisation à la sécurité routière"
        }}
        className="mb-8"
        gridOptions={{
          angle: 60,
          cellSize: 60,
          opacity: 0.3,
          lightLineColor: "#b60062",
          darkLineColor: "#b60062"
        }}
      />

      <div className="container mx-auto px-4 py-4">
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Clock className="h-5 w-5 text-primary" />
              Informations importantes
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className=" bg-card p-4">
                <p className="text-card-foreground">
                  Les stages sont d'une durée de <span className="font-semibold text-primary">14 heures sur 2 jours</span>.
                </p>
                <p className="text-card-foreground">
                  Les horaires journaliers vous seront précisés lors de votre convocation.
                </p>
              </div>

              <div className="rounded-lg border bg-primary/5 p-4">
                <p className="font-medium text-primary">
                  Merci d'arriver 15 minutes avant le début de la formation le matin du premier jour.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="mb-8">
          <CardHeader>
            <CardTitle>Objectifs du stage</CardTitle>
          </CardHeader>
          <CardContent>
            <ul className="list-disc pl-5 space-y-2">
              <li>Réfléchir à la sécurité routière et à son propre comportement sur la route</li>
              <li>Contribuer à réduire le niveau d'exposition au risque d'accident, pour soi et pour les autres</li>
            </ul>
          </CardContent>
        </Card>

        <Card className="mb-8">
          <CardHeader>
            <CardTitle>Moyens pédagogiques</CardTitle>
          </CardHeader>
          <CardContent>
            <ul className="list-disc pl-5 space-y-2">
              <li>Échanges, exercices en sous groupes, études de cas</li>
              <li>Apports de connaissances</li>
              <li>2 co-animateurs : un psychologue et un formateur sécurité routière</li>
              <li>Durée : 2 jours consécutifs</li>
            </ul>
          </CardContent>
        </Card>

        <Card className="mb-8">
          <CardHeader>
            <CardTitle>Horaires</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="p-4 bg-yellow-50 dark:bg-yellow-950/50 rounded-lg border border-yellow-200 dark:border-yellow-900">
              <p className="text-yellow-800 dark:text-yellow-200">
                Le respect des horaires est impératif
              </p>
            </div>
            <p className="mt-4">
              Les stages sont d'une durée de 14 heures sur 2 jours.
              Les horaires journaliers vous seront précisés lors de votre convocation.
              <em className="block mt-2">Merci d'arriver 15 minutes avant le début de la formation le matin du premier jour.</em>
            </p>
          </CardContent>
        </Card>

        <div className="grid gap-8 md:grid-cols-2">
          <Card>
            <CardHeader>
              <CardTitle>Premier jour</CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="space-y-3">
                {[
                  'Accueil des participants',
                  'Tour de table',
                  'Les grands chiffres de la sécurité routière',
                  'La logique de l\'accident',
                  'La lecture de la route',
                  'Nos motivations au volant'
                ].map((item, index) => (
                  <li key={index} className="flex items-start gap-2">
                    <span className="bg-[#b60062]/10 text-[#b60062] rounded-full h-6 w-6 flex items-center justify-center flex-shrink-0 mt-0.5">{index + 1}</span>
                    <div className="pt-1">{item}</div>
                  </li>
                ))}
              </ul>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Deuxième jour</CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="space-y-3">
                {[
                  'Étude d\'un cas d\'accident',
                  'Lois physiques et limites de nos véhicules',
                  'Dispositifs de sécurité active et passive',
                  'Alcool',
                  'Autres perturbateurs de la vigilance',
                  'Synthèse : Portrait du conducteur averti'
                ].map((item, index) => (
                  <li key={index} className="flex items-start gap-2">
                    <span className="bg-[#b60062]/10 text-[#b60062] rounded-full h-6 w-6 flex items-center justify-center flex-shrink-0 mt-0.5">{index + 1}</span>
                    <div className="pt-1">{item}</div>
                  </li>
                ))}
              </ul>
            </CardContent>
          </Card>
        </div>
      </div>
    </FrontLayout>
  );
}


