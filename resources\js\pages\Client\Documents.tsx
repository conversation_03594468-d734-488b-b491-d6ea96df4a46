import { Head } from '@inertiajs/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import ClientLayout from '@/layouts/client-layout';
import { Reservation, ReservationTestPsycho } from '@/types';
import { format, parseISO } from 'date-fns';
import { fr } from 'date-fns/locale';
import { Badge } from '@/components/ui/badge';
import { CalendarDays, FileText, Download } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

interface DocumentsProps {
  stageReservations: Reservation[];
  testReservations: ReservationTestPsycho[];
}

export default function Documents({ stageReservations = [], testReservations = [] }: DocumentsProps) {
  const hasStageDocuments = stageReservations.some(r => 
    r.permis_recto || r.permis_verso || r.lettre_48n_recto || r.lettre_48n_verso
  );
  
  const hasTestDocuments = testReservations.some(r => 
    r.permis_recto || r.permis_verso || r.document_tribunal
  );

  return (
    <ClientLayout title="Mes documents">
      <Head title="Mes documents" />

      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-bold tracking-tight">Mes documents</h1>
        </div>

        {!hasStageDocuments && !hasTestDocuments ? (
          <Card>
            <CardContent className="py-10 text-center">
              <p className="text-muted-foreground">Vous n'avez pas encore de documents.</p>
            </CardContent>
          </Card>
        ) : (
          <Tabs defaultValue="stages" className="space-y-4">
            <TabsList>
              <TabsTrigger value="stages">Documents de stages</TabsTrigger>
              <TabsTrigger value="tests">Documents de tests psychotechniques</TabsTrigger>
            </TabsList>
            
            <TabsContent value="stages">
              {!hasStageDocuments ? (
                <Card>
                  <CardContent className="py-10 text-center">
                    <p className="text-muted-foreground">Vous n'avez pas encore de documents pour vos stages.</p>
                  </CardContent>
                </Card>
              ) : (
                <div className="space-y-4">
                  {stageReservations.filter(r => 
                    r.permis_recto || r.permis_verso || r.lettre_48n_recto || r.lettre_48n_verso
                  ).map((reservation) => (
                    <Card key={reservation.id}>
                      <CardHeader className="pb-2">
                        <div className="flex items-center justify-between">
                          <CardTitle>
                            Stage du {reservation.stage ? format(new Date(reservation.stage?.date_debut), 'dd MMMM yyyy', { locale: fr }) : ''}
                          </CardTitle>
                          <Badge className={
                            reservation.statut === 'confirmée' ? 'bg-green-500' : 
                            reservation.statut === 'en attente' ? 'bg-yellow-500' : 'bg-red-500'
                          }>
                            {reservation.statut}
                          </Badge>
                        </div>
                        <CardDescription>
                          {reservation.stage?.lieu?.nom} - {reservation.stage?.lieu?.ville?.nom}
                        </CardDescription>
                      </CardHeader>
                      <CardContent>
                        <div className="grid gap-4">
                          <div className="flex items-center gap-2">
                            <CalendarDays className="h-4 w-4 text-muted-foreground" />
                            <span>
                              Réservé le {format(new Date(reservation.date_reservation), 'dd/MM/yyyy', { locale: fr })}
                            </span>
                          </div>
                          
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-2">
                            {reservation.permis_recto && (
                              <div className="flex items-center justify-between p-3 border rounded-md">
                                <div className="flex items-center gap-2">
                                  <FileText className="h-5 w-5 text-blue-500" />
                                  <span>Permis (recto)</span>
                                </div>
                                <Button variant="outline" size="sm" asChild>
                                  <a href={`/files/${reservation.permis_recto}`} target="_blank">
                                    <Download className="h-4 w-4 mr-1" />
                                    Télécharger
                                  </a>
                                </Button>
                              </div>
                            )}
                            
                            {reservation.permis_verso && (
                              <div className="flex items-center justify-between p-3 border rounded-md">
                                <div className="flex items-center gap-2">
                                  <FileText className="h-5 w-5 text-blue-500" />
                                  <span>Permis (verso)</span>
                                </div>
                                <Button variant="outline" size="sm" asChild>
                                  <a href={`/files/${reservation.permis_verso}`} target="_blank">
                                    <Download className="h-4 w-4 mr-1" />
                                    Télécharger
                                  </a>
                                </Button>
                              </div>
                            )}
                            
                            {reservation.lettre_48n_recto && (
                              <div className="flex items-center justify-between p-3 border rounded-md">
                                <div className="flex items-center gap-2">
                                  <FileText className="h-5 w-5 text-blue-500" />
                                  <span>Lettre 48N (recto)</span>
                                </div>
                                <Button variant="outline" size="sm" asChild>
                                  <a href={`/files/${reservation.lettre_48n_recto}`} target="_blank">
                                    <Download className="h-4 w-4 mr-1" />
                                    Télécharger
                                  </a>
                                </Button>
                              </div>
                            )}
                            
                            {reservation.lettre_48n_verso && (
                              <div className="flex items-center justify-between p-3 border rounded-md">
                                <div className="flex items-center gap-2">
                                  <FileText className="h-5 w-5 text-blue-500" />
                                  <span>Lettre 48N (verso)</span>
                                </div>
                                <Button variant="outline" size="sm" asChild>
                                  <a href={`/files/${reservation.lettre_48n_verso}`} target="_blank">
                                    <Download className="h-4 w-4 mr-1" />
                                    Télécharger
                                  </a>
                                </Button>
                              </div>
                            )}
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </TabsContent>
            
            <TabsContent value="tests">
              {!hasTestDocuments ? (
                <Card>
                  <CardContent className="py-10 text-center">
                    <p className="text-muted-foreground">Vous n'avez pas encore de documents pour vos tests psychotechniques.</p>
                  </CardContent>
                </Card>
              ) : (
                <div className="space-y-4">
                  {testReservations.filter(r => 
                    r.permis_recto || r.permis_verso || r.document_tribunal
                  ).map((reservation) => (
                    <Card key={reservation.id}>
                      <CardHeader className="pb-2">
                        <div className="flex items-center justify-between">
                          <CardTitle>
                            Test du {reservation.test_psycho?.date
                              ? format(parseISO(reservation.test_psycho.date), 'dd MMMM yyyy', { locale: fr })
                              : 'N/A'}
                          </CardTitle>
                          <Badge className={
                            reservation.statut === 'confirmée' ? 'bg-green-500' : 
                            reservation.statut === 'en attente' ? 'bg-yellow-500' : 'bg-red-500'
                          }>
                            {reservation.statut}
                          </Badge>
                        </div>
                        <CardDescription>
                          {reservation.type_test_psycho?.nom || 'Type de test non spécifié'}
                        </CardDescription>
                      </CardHeader>
                      <CardContent>
                        <div className="grid gap-4">
                          <div className="flex items-center gap-2">
                            <CalendarDays className="h-4 w-4 text-muted-foreground" />
                            <span>
                              Réservé le {format(parseISO(reservation.date_reservation), 'dd/MM/yyyy', { locale: fr })}
                            </span>
                          </div>
                          
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-2">
                            {reservation.permis_recto && (
                              <div className="flex items-center justify-between p-3 border rounded-md">
                                <div className="flex items-center gap-2">
                                  <FileText className="h-5 w-5 text-blue-500" />
                                  <span>Permis (recto)</span>
                                </div>
                                <Button variant="outline" size="sm" asChild>
                                  <a href={`/files/${reservation.permis_recto}`} target="_blank">
                                    <Download className="h-4 w-4 mr-1" />
                                    Télécharger
                                  </a>
                                </Button>
                              </div>
                            )}
                            
                            {reservation.permis_verso && (
                              <div className="flex items-center justify-between p-3 border rounded-md">
                                <div className="flex items-center gap-2">
                                  <FileText className="h-5 w-5 text-blue-500" />
                                  <span>Permis (verso)</span>
                                </div>
                                <Button variant="outline" size="sm" asChild>
                                  <a href={`/files/${reservation.permis_verso}`} target="_blank">
                                    <Download className="h-4 w-4 mr-1" />
                                    Télécharger
                                  </a>
                                </Button>
                              </div>
                            )}
                            
                            {reservation.document_tribunal && (
                              <div className="flex items-center justify-between p-3 border rounded-md">
                                <div className="flex items-center gap-2">
                                  <FileText className="h-5 w-5 text-blue-500" />
                                  <span>Document tribunal</span>
                                </div>
                                <Button variant="outline" size="sm" asChild>
                                  <a href={`/files/${reservation.document_tribunal}`} target="_blank">
                                    <Download className="h-4 w-4 mr-1" />
                                    Télécharger
                                  </a>
                                </Button>
                              </div>
                            )}
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </TabsContent>
          </Tabs>
        )}
      </div>
    </ClientLayout>
  );
}
