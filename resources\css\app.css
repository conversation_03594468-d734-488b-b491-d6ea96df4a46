@import 'tailwindcss';

@plugin 'tailwindcss-animate';

@source '../views';
@source '../../vendor/laravel/framework/src/Illuminate/Pagination/resources/views/*.blade.php';

@custom-variant dark (&:is(.dark *));

@theme {
    --font-sans:
        'Instrument Sans', ui-sans-serif, system-ui, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
    --font-heading:
        'Oswald', sans-serif;

    --radius-lg: var(--radius);
    --radius-md: calc(var(--radius) - 2px);
    --radius-sm: calc(var(--radius) - 4px);

    --color-background: var(--background);
    --color-foreground: var(--foreground);

    --color-card: var(--card);
    --color-card-foreground: var(--card-foreground);

    --color-popover: var(--popover);
    --color-popover-foreground: var(--popover-foreground);

    --color-primary: var(--primary);
    --color-primary-foreground: var(--primary-foreground);

    --color-secondary: var(--secondary);
    --color-secondary-foreground: var(--secondary-foreground);

    --color-muted: var(--muted);
    --color-muted-foreground: var(--muted-foreground);

    --color-accent: var(--accent);
    --color-accent-foreground: var(--accent-foreground);

    --color-destructive: var(--destructive);
    --color-destructive-foreground: var(--destructive-foreground);

    --color-border: var(--border);
    --color-input: var(--input);
    --color-ring: var(--ring);

    --color-chart-1: var(--chart-1);
    --color-chart-2: var(--chart-2);
    --color-chart-3: var(--chart-3);
    --color-chart-4: var(--chart-4);
    --color-chart-5: var(--chart-5);

    --color-sidebar: var(--sidebar);
    --color-sidebar-foreground: var(--sidebar-foreground);
    --color-sidebar-primary: var(--sidebar-primary);
    --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
    --color-sidebar-accent: var(--sidebar-accent);
    --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
    --color-sidebar-border: var(--sidebar-border);
    --color-sidebar-ring: var(--sidebar-ring);
}

/*
  The default border color has changed to `currentColor` in Tailwind CSS v4,
  so we've added these compatibility styles to make sure everything still
  looks the same as it did with Tailwind CSS v3.

  If we ever want to remove these styles, we need to add an explicit border
  color utility to any element that depends on these defaults.
*/
@layer base {
    *,
    ::after,
    ::before,
    ::backdrop,
    ::file-selector-button {
        border-color: var(--color-gray-200, currentColor);
    }
}

:root {
    --background: oklch(1 0 0);
    --foreground: oklch(0.145 0 0);
    --card: oklch(1 0 0);
    --card-foreground: oklch(0.145 0 0);
    --popover: oklch(1 0 0);
    --popover-foreground: oklch(0.145 0 0);
    --primary: oklch(0.466 0.257 354.3);
    --primary-foreground: oklch(0.985 0 0);
    --secondary: oklch(0.97 0 0);
    --secondary-foreground: oklch(0.205 0 0);
    --muted: oklch(0.97 0 0);
    --muted-foreground: oklch(0.556 0 0);
    --accent: oklch(0.97 0 0);
    --accent-foreground: oklch(0.205 0 0);
    --destructive: oklch(0.577 0.245 27.325);
    --destructive-foreground: oklch(0.577 0.245 27.325);
    --border: oklch(0.922 0 0);
    --input: oklch(0.922 0 0);
    --ring: oklch(0.87 0 0);
    --chart-1: hsl(143, 89%, 58%);
    --chart-2: hsl(31 97% 72%);
    --chart-3: hsl(0, 96%, 61%);
    --chart-4: hsl(210 98% 78%);
    --chart-5: hsl(212 97% 87%);
    --radius: 0.625rem;
    --sidebar: oklch(0.985 0 0);
    --sidebar-foreground: oklch(0.145 0 0);
    --sidebar-primary: oklch(0.205 0 0);
    --sidebar-primary-foreground: oklch(0.985 0 0);
    --sidebar-accent: oklch(0.97 0 0);
    --sidebar-accent-foreground: oklch(0.205 0 0);
    --sidebar-border: oklch(0.922 0 0);
    --sidebar-ring: oklch(0.87 0 0);
    --brand:hsl(27 96% 61%);
    --brand-foreground:hsl(31 97% 72%);
}

.dark {
    --background: oklch(0.145 0 0);
    --foreground: oklch(0.985 0 0);
    --card: oklch(0.145 0 0);
    --card-foreground: oklch(0.985 0 0);
    --popover: oklch(0.145 0 0);
    --popover-foreground: oklch(0.985 0 0);
    --primary: oklch(0.466 0.257 354.3);
    --primary-foreground: oklch(0.205 0 0);
    --secondary: oklch(0.269 0 0);
    --secondary-foreground: oklch(0.985 0 0);
    --muted: oklch(0.269 0 0);
    --muted-foreground: oklch(0.708 0 0);
    --accent: oklch(0.269 0 0);
    --accent-foreground: oklch(0.985 0 0);
    --destructive: oklch(0.396 0.141 25.723);
    --destructive-foreground: oklch(0.637 0.237 25.331);
    --border: oklch(0.269 0 0);
    --input: oklch(0.269 0 0);
    --ring: oklch(0.439 0 0);
    --chart-1: hsl(143, 61%, 69%);
    --chart-2: hsl(31, 59%, 53%);
    --chart-3: hsl(0, 63%, 57%);
    --chart-4: hsl(210 98% 78%);
    --chart-5: oklch(0.645 0.246 16.439);
    --sidebar: oklch(0.205 0 0);
    --sidebar-foreground: oklch(0.985 0 0);
    --sidebar-primary: oklch(0.985 0 0);
    --sidebar-primary-foreground: oklch(0.985 0 0);
    --sidebar-accent: oklch(0.269 0 0);
    --sidebar-accent-foreground: oklch(0.985 0 0);
    --sidebar-border: oklch(0.269 0 0);
    --sidebar-ring: oklch(0.439 0 0);
    --brand:hsl(31 97% 72%);
}

@layer base {
    * {
        @apply border-border;
    }

    body {
        @apply bg-background text-foreground;
    }
}

/*
  ---break---
*/

@theme inline {
    --color-brand:
        var(----brand);
    --color-brand-foreground:
        var(----brand-foreground);
}

/*
  ---break---
*/

@layer base {
  * {
    @apply border-border outline-ring/50;
    }
  body {
    @apply bg-background text-foreground;
    }
  .font-heading {
    font-family: var(--font-heading);
    }
  h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-heading);
    }
}

/* Styles pour Recharts */
.recharts-wrapper {
  width: 100% !important;
  height: 100% !important;
}

.recharts-surface {
  overflow: visible;
}

.recharts-default-tooltip {
  background-color: var(--background) !important;
  border: 1px solid var(--border) !important;
  border-radius: 0.5rem !important;
  padding: 0.5rem !important;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1) !important;
}

.recharts-tooltip-item-name,
.recharts-tooltip-item-value {
  color: var(--foreground) !important;
}

/* Safari-specific optimizations */
@supports (-webkit-appearance: none) {
  /* Safari-specific styles */

  /* Optimize background-attachment for Safari */
  .bg-fixed {
    background-attachment: scroll;
  }

  /* Improve transform performance in Safari */
  .transform {
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
  }

  /* Optimize animations for Safari */
  .animate-pulse,
  .animate-spin,
  .animate-bounce {
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
  }

  /* Improve backdrop-filter support */
  .backdrop-blur-sm,
  .backdrop-blur,
  .backdrop-blur-md,
  .backdrop-blur-lg {
    -webkit-backdrop-filter: blur(8px);
    backdrop-filter: blur(8px);
  }

  /* Optimize gradient rendering */
  .bg-gradient-to-r,
  .bg-gradient-to-l,
  .bg-gradient-to-t,
  .bg-gradient-to-b,
  .bg-gradient-to-tr,
  .bg-gradient-to-tl,
  .bg-gradient-to-br,
  .bg-gradient-to-bl {
    -webkit-background-clip: padding-box;
    background-clip: padding-box;
  }

  /* Improve text rendering */
  .text-transparent {
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
  }

  /* Optimize shadow rendering */
  .shadow-sm,
  .shadow,
  .shadow-md,
  .shadow-lg,
  .shadow-xl {
    -webkit-box-shadow: var(--tw-shadow);
    box-shadow: var(--tw-shadow);
  }
}

/* Additional Safari compatibility for complex animations */
@media screen and (-webkit-min-device-pixel-ratio: 0) {
  /* Target WebKit browsers (Safari, older Chrome) */

  /* Disable complex transforms that cause issues */
  .safari-simple-transform {
    transform: none !important;
    transition: opacity 0.3s ease !important;
  }

  /* Simplify filter effects */
  .safari-no-filter {
    filter: none !important;
    backdrop-filter: none !important;
  }

  /* Optimize background images */
  .safari-bg-optimize {
    background-attachment: scroll !important;
    will-change: auto !important;
  }
}

