<?php

namespace Database\Seeders;

use App\Models\TestPsycho;
use App\Models\Lieu;
use Illuminate\Database\Seeder;

class TestPsychoSeeder extends Seeder
{
    public function run(): void
    {
        $lieux = Lieu::all();

        if ($lieux->isEmpty()) {
            // Si aucun lieu n'existe, on ne peut pas créer de tests psychotechniques
            return;
        }

        // Créer 15 tests psychotechniques pour les 3 prochains mois
        for ($i = 0; $i < 15; $i++) {
            $date = now()->addDays(rand(7, 90));

            TestPsycho::create([
                'date' => $date,
                'lieu_id' => $lieux->random()->id,
                'places_disponibles' => rand(5, 15),
                'prix' => rand(100, 250),
                'reference' => 'TP-' . strtoupper(substr(str_shuffle('ABCDEFGHIJKLMNOPQRSTUVWXYZ'), 0, 2) . rand(100, 999)),
            ]);
        }
    }
}
