<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Configuration;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Inertia\Inertia;

class ConfigurationController extends Controller
{
    /**
     * Display the configuration management page
     */
    public function index()
    {
        // Get all configurations grouped by category
        $configurations = Configuration::all()->groupBy('group');

        // Format payment methods for easier frontend handling
        $paymentMethods = [
            'paypal' => Configuration::get('payment_method_paypal_enabled', true),
            'sumup' => Configuration::get('payment_method_sumup_enabled', true),
            'virement' => Configuration::get('payment_method_virement_enabled', true),
            'cheque' => Configuration::get('payment_method_cheque_enabled', true),
            'bon' => Configuration::get('payment_method_bon_enabled', true),
            'paiement_sur_place' => Configuration::get('payment_method_paiement_sur_place_enabled', true),
        ];

        return Inertia::render('Admin/Configuration/Index', [
            'configurations' => $configurations,
            'paymentMethods' => $paymentMethods,
        ]);
    }

    /**
     * Update payment method configurations
     */
    public function updatePaymentMethods(Request $request)
    {
        $validated = $request->validate([
            'paypal' => 'required|boolean',
            'sumup' => 'required|boolean',
            'virement' => 'required|boolean',
            'cheque' => 'required|boolean',
            'bon' => 'required|boolean',
            'paiement_sur_place' => 'required|boolean',
        ]);

        try {
            // Update each payment method configuration
            foreach ($validated as $method => $enabled) {
                Configuration::set(
                    "payment_method_{$method}_enabled",
                    $enabled,
                    'boolean',
                    "Enable/disable {$method} payment method",
                    'payment_methods'
                );
            }

            // Clear configuration cache
            Configuration::clearCache();

            return redirect()->back()->with('success', 'Configuration des méthodes de paiement mise à jour avec succès.');
        } catch (\Exception $e) {
            Log::error('Failed to update payment method configuration', [
                'error' => $e->getMessage(),
                'data' => $validated
            ]);

            return redirect()->back()->with('error', 'Une erreur est survenue lors de la mise à jour de la configuration.');
        }
    }

    /**
     * Update general configurations
     */
    public function updateGeneral(Request $request)
    {
        $validated = $request->validate([
            'app_maintenance_mode' => 'required|boolean',
            'max_reservations_per_user' => 'required|integer|min:1|max:100',
        ]);

        try {
            Configuration::set(
                'app_maintenance_mode',
                $validated['app_maintenance_mode'],
                'boolean',
                'Enable/disable application maintenance mode',
                'general'
            );

            Configuration::set(
                'max_reservations_per_user',
                $validated['max_reservations_per_user'],
                'integer',
                'Maximum number of reservations per user',
                'general'
            );

            // Clear configuration cache
            Configuration::clearCache();

            return redirect()->back()->with('success', 'Configuration générale mise à jour avec succès.');
        } catch (\Exception $e) {
            Log::error('Failed to update general configuration', [
                'error' => $e->getMessage(),
                'data' => $validated
            ]);

            return redirect()->back()->with('error', 'Une erreur est survenue lors de la mise à jour de la configuration.');
        }
    }
}
