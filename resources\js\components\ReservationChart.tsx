import { TrendingUp, TrendingDown } from "lucide-react";
import { <PERSON>, <PERSON><PERSON><PERSON>, CartesianGrid, XAxis, <PERSON><PERSON><PERSON>, ResponsiveContainer } from "recharts";

import {
  Card,
  CardContent,
  CardDescription,
  <PERSON><PERSON>ooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";

interface ReservationChartProps {
  data: {
    month: string;
    stages: number;
    tests: number;
  }[];
  trend: {
    percentage: number;
    isUp: boolean;
  };
}

export function ReservationChart({ data, trend }: ReservationChartProps) {
  // Vérifier si les données sont valides
  const hasValidData = Array.isArray(data) && data.length > 0;

  console.log("Chart Data:", data);
  console.log("Chart Trend:", trend);

  return (
    <Card>
      <CardHeader>
        <CardTitle>Réservations par mois</CardTitle>
        <CardDescription>Statistiques des 6 derniers mois</CardDescription>
      </CardHeader>
      <CardContent>
        {!hasValidData ? (
          <div className="flex h-[300px] items-center justify-center text-muted-foreground">
            Aucune donnée disponible pour le graphique
          </div>
        ) : (
          <div className="h-[300px] w-full">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart
                data={data}
                margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                barGap={4}
                barCategoryGap={16}
              >
                <CartesianGrid vertical={false} strokeDasharray="3 3" />
                <XAxis
                  dataKey="month"
                  tickLine={false}
                  tickMargin={10}
                  axisLine={false}
                  tickFormatter={(value) => value.slice(0, 3)}
                />
                <Tooltip
                  cursor={{ fill: 'rgba(0, 0, 0, 0.1)' }}
                  contentStyle={{
                    backgroundColor: 'var(--background)',
                    border: '1px solid var(--border)',
                    borderRadius: '0.5rem',
                    padding: '0.5rem'
                  }}
                />
                <Bar
                  name="Stages"
                  dataKey="stages"
                  fill="hsl(215, 100%, 50%)"
                  radius={4}
                />
                <Bar
                  name="Tests Psycho"
                  dataKey="tests"
                  fill="hsl(280, 100%, 60%)"
                  radius={4}
                />
              </BarChart>
            </ResponsiveContainer>
          </div>
        )}
      </CardContent>
      <CardFooter className="flex-col items-start gap-2 text-sm">
        <div className="flex gap-2 font-medium leading-none">
          {trend.isUp ? (
            <>
              En hausse de {trend.percentage}% ce mois-ci <TrendingUp className="h-4 w-4 text-green-500" />
            </>
          ) : (
            <>
              En baisse de {trend.percentage}% ce mois-ci <TrendingDown className="h-4 w-4 text-red-500" />
            </>
          )}
        </div>
        <div className="leading-none text-muted-foreground">
          Affichage des réservations totales pour les 6 derniers mois
        </div>
      </CardFooter>
    </Card>
  );
}
