import { Head, router } from '@inertiajs/react';
import { PageProps, Ville, Departement, BreadcrumbItem, PaginatedData } from '@/types';
import DataTable from '@/components/DataTable';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { useState } from 'react';
import { useForm } from '@inertiajs/react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import AppLayout from '@/layouts/app-layout';

interface VillesPageProps extends PageProps {
    villes: PaginatedData<Ville>;
    departements: Departement[];
}

export default function Index({ villes, departements }: VillesPageProps) {
    const [isOpen, setIsOpen] = useState(false);
    const [editingVille, setEditingVille] = useState<Ville | null>(null);

    const form = useForm({
        nom: '',
        departement_id: '',
        code: '',
        reg_code: '',
        dep_code: '',
        code_postal: '',
    });

    const columns = [
        { key: 'nom', label: 'Nom' },
        { key: 'code_postal', label: 'Code Postal' },
        {
            key: 'departement',
            label: 'Département',
            render: (value: unknown, row: Record<string, unknown>) => ((row as unknown) as Ville).departement?.nom || ''
        },
        {
            key: 'lieus',
            label: 'Lieux',
            render: (value: unknown) => Array.isArray(value) ? value.length : 0,
        },
    ];

    const handleAdd = () => {
        setEditingVille(null);
        form.reset();
        setIsOpen(true);
    };

    const handleEdit = (row: Record<string, unknown>) => {
        const ville = row as unknown as Ville;
        setEditingVille(ville);
        form.setData({
            nom: ville.nom,
            departement_id: ville.departement_id.toString(),
            code: ville.code || '',
            reg_code: ville.reg_code || '',
            dep_code: ville.dep_code || '',
            code_postal: ville.code_postal || '',
        });
        setIsOpen(true);
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        if (editingVille) {
            form.put(route('admin.villes.update', editingVille.id), {
                onSuccess: () => setIsOpen(false),
            });
        } else {
            form.post(route('admin.villes.store'), {
                onSuccess: () => setIsOpen(false),
            });
        }
    };

    const handleDelete = (row: Record<string, unknown>) => {
        const ville = row as unknown as Ville;
        if (confirm('Êtes-vous sûr de vouloir supprimer cette ville ?')) {
            router.delete(route('admin.villes.destroy', ville.id));
        }
    };

    const breadcrumbs: BreadcrumbItem[] = [
        {
            title: 'Villes',
            href: '/admin/villes',
        },
    ];

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Villes" />
            <div className="flex h-full flex-1 flex-col gap-4 rounded-xl p-4">
                <DataTable
                    title="Villes"
                    columns={columns}
                    data={villes.data.map(ville => ({
                        id: ville.id,
                        nom: ville.nom,
                        code: ville.code,
                        reg_code: ville.reg_code,
                        dep_code: ville.dep_code,
                        code_postal: ville.code_postal,
                        departement: ville.departement,
                        lieus: ville.lieus,
                    }))}
                    onAdd={handleAdd}
                    onEdit={handleEdit}
                    onDelete={handleDelete}
                    pagination={{
                        links: villes.links,
                        from: villes.from,
                        to: villes.to,
                        total: villes.total
                    }}
                />

                <Dialog open={isOpen} onOpenChange={setIsOpen}>
                    <DialogContent>
                        <DialogHeader>
                            <DialogTitle>
                                {editingVille ? 'Modifier la ville' : 'Ajouter une ville'}
                            </DialogTitle>
                        </DialogHeader>
                        <form onSubmit={handleSubmit} className="space-y-4">
                            <div>
                                <Input
                                    placeholder="Nom de la ville"
                                    value={form.data.nom}
                                    onChange={e => form.setData('nom', e.target.value)}
                                />
                            </div>
                            <div>
                                <Select
                                    value={form.data.departement_id}
                                    onValueChange={(value) => form.setData('departement_id', value)}
                                >
                                    <SelectTrigger>
                                        <SelectValue placeholder="Sélectionner un département" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        {departements.map((departement) => (
                                            <SelectItem
                                                key={departement.id}
                                                value={departement.id.toString()}
                                            >
                                                {departement.nom} ({departement.code})
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                            </div>
                            <div className="flex justify-end space-x-2">
                                <Button
                                    type="button"
                                    variant="outline"
                                    onClick={() => setIsOpen(false)}
                                >
                                    Annuler
                                </Button>
                                <Button type="submit" disabled={form.processing}>
                                    {editingVille ? 'Modifier' : 'Ajouter'}
                                </Button>
                            </div>
                        </form>
                    </DialogContent>
                </Dialog>
            </div>
        </AppLayout>
    );
}


