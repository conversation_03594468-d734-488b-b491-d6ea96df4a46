import FrontLayout from '@/layouts/front-layout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { MapPin, Phone, Mail, Clock, Facebook, Instagram, Linkedin, RefreshCw } from 'lucide-react';
import { useForm } from '@inertiajs/react';
import { FormEvent, useEffect, useState } from 'react';
import { toast } from 'sonner';

export default function Contact() {
  const [captchaImage, setCaptchaImage] = useState<string>('');
  const [captchaHash, setCaptchaHash] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(true);

  const { data, setData, post, processing, errors, reset } = useForm({
    nom: '',
    prenom: '',
    email: '',
    telephone: '',
    sujet: '',
    message: '',
    rgpd: false,
    captcha: '',
    captcha_hash: '',
  });

  // Charger le CAPTCHA au chargement du composant
  useEffect(() => {
    loadCaptcha();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Fonction pour charger un nouveau CAPTCHA
  const loadCaptcha = async () => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/captcha');
      const data = await response.json();
      setCaptchaImage(data.captcha);
      setCaptchaHash(data.hashed_captcha_text);
      setData('captcha_hash', data.hashed_captcha_text);
    } catch (error) {
      console.error('Erreur lors du chargement du CAPTCHA:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSubmit = (e: FormEvent) => {
    e.preventDefault();

    post(route('contact.store'), {
      onSuccess: () => {
        reset('nom', 'prenom', 'email', 'telephone', 'sujet', 'message', 'rgpd', 'captcha');
        loadCaptcha(); // Recharger un nouveau CAPTCHA après l'envoi
        // toast.success('Votre message a été envoyé avec succès. Nous vous répondrons dans les plus brefs délais.');
      },
    });
  };

  return (
    <FrontLayout title="Contact">
      <div className="container mx-auto px-4 py-8">
        <h1 className="mb-6 text-3xl font-bold">Contactez-nous</h1>

        <div className="grid gap-8 md:grid-cols-2 mb-8">
          <Card>
            <CardHeader>
              <CardTitle>Formulaire de contact</CardTitle>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-4">
                <div className="grid gap-4 md:grid-cols-2">
                  <div>
                    <label htmlFor="nom" className="block text-sm font-medium mb-1">
                      Nom <span className="text-red-500">*</span>
                    </label>
                    <Input
                      id="nom"
                      name="nom"
                      value={data.nom}
                      onChange={e => setData('nom', e.target.value)}
                      placeholder="Votre nom"
                      className={errors.nom ? 'border-red-500' : ''}
                    />
                    {errors.nom && <p className="mt-1 text-sm text-red-500">{errors.nom}</p>}
                  </div>
                  <div>
                    <label htmlFor="prenom" className="block text-sm font-medium mb-1">
                      Prénom <span className="text-red-500">*</span>
                    </label>
                    <Input
                      id="prenom"
                      name="prenom"
                      value={data.prenom}
                      onChange={e => setData('prenom', e.target.value)}
                      placeholder="Votre prénom"
                      className={errors.prenom ? 'border-red-500' : ''}
                    />
                    {errors.prenom && <p className="mt-1 text-sm text-red-500">{errors.prenom}</p>}
                  </div>
                </div>

                <div>
                  <label htmlFor="email" className="block text-sm font-medium mb-1">
                    Email <span className="text-red-500">*</span>
                  </label>
                  <Input
                    id="email"
                    name="email"
                    type="email"
                    value={data.email}
                    onChange={e => setData('email', e.target.value)}
                    placeholder="<EMAIL>"
                    className={errors.email ? 'border-red-500' : ''}
                  />
                  {errors.email && <p className="mt-1 text-sm text-red-500">{errors.email}</p>}
                </div>

                <div>
                  <label htmlFor="telephone" className="block text-sm font-medium mb-1">Téléphone</label>
                  <Input
                    id="telephone"
                    name="telephone"
                    value={data.telephone}
                    onChange={e => setData('telephone', e.target.value)}
                    placeholder="06 XX XX XX XX"
                    className={errors.telephone ? 'border-red-500' : ''}
                  />
                  {errors.telephone && <p className="mt-1 text-sm text-red-500">{errors.telephone}</p>}
                </div>

                <div>
                  <label htmlFor="sujet" className="block text-sm font-medium mb-1">
                    Sujet <span className="text-red-500">*</span>
                  </label>
                  <Select
                    name="sujet"
                    value={data.sujet}
                    onValueChange={(value) => setData('sujet', value)}
                  >
                    <SelectTrigger id="sujet" className={errors.sujet ? 'border-red-500' : ''}>
                      <SelectValue placeholder="Choisir un sujet" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="stage">Stages de sensibilisation à la sécurité routière</SelectItem>
                      <SelectItem value="test">Test psychotechnique</SelectItem>
                      <SelectItem value="pssm">Formation PSSM</SelectItem>
                      <SelectItem value="autre">Autre demande</SelectItem>
                    </SelectContent>
                  </Select>
                  {errors.sujet && <p className="mt-1 text-sm text-red-500">{errors.sujet}</p>}
                </div>

                <div>
                  <label htmlFor="message" className="block text-sm font-medium mb-1">
                    Message <span className="text-red-500">*</span>
                  </label>
                  <Textarea
                    id="message"
                    name="message"
                    value={data.message}
                    onChange={e => setData('message', e.target.value)}
                    placeholder="Votre message"
                    rows={5}
                    className={errors.message ? 'border-red-500' : ''}
                  />
                  {errors.message && <p className="mt-1 text-sm text-red-500">{errors.message}</p>}
                </div>

                <div className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    id="rgpd"
                    name="rgpd"
                    checked={data.rgpd as boolean}
                    onChange={e => setData('rgpd', e.target.checked)}
                    className={`h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary ${errors.rgpd ? 'border-red-500' : ''}`}
                  />
                  <label htmlFor="rgpd" className="text-sm text-muted-foreground">
                    J'accepte que mes données soient traitées conformément à la politique de confidentialité <span className="text-red-500">*</span>
                  </label>
                </div>
                {errors.rgpd && <p className="mt-1 text-sm text-red-500">{errors.rgpd}</p>}

                <div className="space-y-2">
                  <label htmlFor="captcha" className="block text-sm font-medium mb-1">
                    Code de vérification <span className="text-red-500">*</span>
                  </label>
                  <div className="flex flex-col gap-3 sm:flex-row sm:items-center">
                    <div className="relative">
                      {isLoading ? (
                        <div className="h-12 w-32 bg-gray-200 dark:bg-gray-700 animate-pulse rounded"></div>
                      ) : (
                        <img
                          src={captchaImage}
                          alt="CAPTCHA"
                          className="h-12 border rounded bg-white dark:bg-gray-800"
                        />
                      )}
                    </div>
                    <div className="flex gap-2 items-center flex-1">
                      <Input
                        id="captcha"
                        name="captcha"
                        value={data.captcha}
                        onChange={e => setData('captcha', e.target.value)}
                        placeholder="Saisissez le code"
                        className={errors.captcha ? 'border-red-500' : ''}
                      />
                      <Button
                        type="button"
                        variant="outline"
                        size="icon"
                        onClick={() => loadCaptcha()}
                        disabled={isLoading}
                      >
                        <RefreshCw className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                  {errors.captcha && <p className="mt-1 text-sm text-red-500">{errors.captcha}</p>}
                </div>

                <Button type="submit" className="w-full" disabled={processing}>
                  {processing ? 'Envoi en cours...' : 'Envoyer'}
                </Button>
              </form>
            </CardContent>
          </Card>

          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Nos coordonnées</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-start gap-3">
                    <MapPin className="h-5 w-5 text-primary flex-shrink-0 mt-0.5" />
                    <div>
                      <p className="font-medium">Adresse</p>
                      <p className="text-muted-foreground">Palais Vauban</p>
                      <p className="text-muted-foreground">12 avenue Jean Moulin</p>
                      <p className="text-muted-foreground">83000 TOULON</p>
                    </div>
                  </div>

                  <div className="flex items-start gap-3">
                    <Phone className="h-5 w-5 text-primary flex-shrink-0 mt-0.5" />
                    <div>
                      <p className="font-medium">Téléphone</p>
                      <p className="text-muted-foreground">06 58 77 23 85</p>
                    </div>
                  </div>

                  <div className="flex items-start gap-3">
                    <Mail className="h-5 w-5 text-primary flex-shrink-0 mt-0.5" />
                    <div>
                      <p className="font-medium">Email</p>
                      <p className="text-muted-foreground"><EMAIL></p>
                    </div>
                  </div>

                  <div className="flex items-start gap-3">
                    <Clock className="h-5 w-5 text-primary flex-shrink-0 mt-0.5" />
                    <div>
                      <p className="font-medium">Horaires d'ouverture</p>
                      <p className="text-muted-foreground">Du lundi au vendredi</p>
                      <p className="text-muted-foreground">9h00 - 12h30 | 14h00 - 18h00</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Suivez-nous</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex gap-4">
                  <a href="#" className="flex items-center justify-center h-10 w-10 rounded-full bg-primary/10 hover:bg-primary/20 transition-colors">
                    <Facebook className="h-5 w-5 text-primary" />
                  </a>
                  <a href="#" className="flex items-center justify-center h-10 w-10 rounded-full bg-primary/10 hover:bg-primary/20 transition-colors">
                    <Instagram className="h-5 w-5 text-primary" />
                  </a>
                  <a href="#" className="flex items-center justify-center h-10 w-10 rounded-full bg-primary/10 hover:bg-primary/20 transition-colors">
                    <Linkedin className="h-5 w-5 text-primary" />
                  </a>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Besoin d'une réponse rapide ?</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="mb-4">
                  Pour toute question urgente concernant nos stages ou tests, n'hésitez pas à nous appeler directement.
                </p>
                <Button variant="outline" className="w-full" asChild>
                  <a href="tel:0658772385">Appeler maintenant</a>
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Nous trouver</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="aspect-video rounded-md overflow-hidden">
              <iframe
                src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d2916.1088520354985!2d5.9278!3d43.1254!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x12c91b0f7f8eaacf%3A0x2c8c7b4b9a2b8a0!2s12%20Av.%20Jean%20Moulin%2C%2083000%20Toulon!5e0!3m2!1sfr!2sfr!4v1650000000000!5m2!1sfr!2sfr"
                width="100%"
                height="100%"
                style={{ border: 0 }}
                allowFullScreen
                loading="lazy"
                referrerPolicy="no-referrer-when-downgrade"
              ></iframe>
            </div>
          </CardContent>
        </Card>
      </div>
    </FrontLayout>
  );
}
