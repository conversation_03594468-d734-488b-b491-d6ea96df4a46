<?php

namespace Database\Factories;

use App\Models\Departement;
use Illuminate\Database\Eloquent\Factories\Factory;

class VilleFactory extends Factory
{
    public function definition(): array
    {
        return [
            'nom' => fake()->unique()->city(),
            'departement_id' => Departement::factory(),
        ];
    }

    /**
     * Configure the model factory.
     *
     * @return $this
     */
    public function configure()
    {
        return $this->afterCreating(function ($ville) {
            // If you need to create related Lieu models, use the correct plural form
            // $ville->lieus()->create([...]);
        });
    }
}
