import * as React from "react"
import { Table } from "@tanstack/react-table"
import { X } from "lucide-react"

import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { DataTableViewOptions } from "./data-table-view-options"

interface DataTableToolbarProps<TData> {
  table: Table<TData>
  filterableColumns?: {
    id: string
    title: string
    options: {
      value: string
      label: string
    }[]
  }[]
  searchableColumns?: {
    id: string
    title: string
  }[]
}

export function DataTableToolbar<TData>({
  table,
  filterableColumns = [],
  searchableColumns = [],
}: DataTableToolbarProps<TData>) {
  const isFiltered = table.getState().columnFilters.length > 0

  return (
    <div className="flex flex-wrap items-center justify-between gap-2">
      <div className="flex flex-1 flex-wrap items-center gap-2">
        {searchableColumns.length > 0 &&
          searchableColumns.map(
            (column) => (
              <div key={column.id} className="flex-1">
                <Input
                  placeholder={`Rechercher par ${column.title.toLowerCase()}...`}
                  value={(table.getColumn(column.id)?.getFilterValue() as string) ?? ""}
                  onChange={(event) =>
                    table.getColumn(column.id)?.setFilterValue(event.target.value)
                  }
                  className="h-8 w-full"
                />
              </div>
            )
          )}
        {filterableColumns.length > 0 &&
          filterableColumns.map(
            (column) => (
              <div key={column.id} className="flex-1">
                <Select
                  value={(table.getColumn(column.id)?.getFilterValue() as string) ?? "all"}
                  onValueChange={(value) =>
                    table.getColumn(column.id)?.setFilterValue(value === "all" ? "" : value)
                  }
                >
                  <SelectTrigger className="h-8 w-full">
                    <SelectValue placeholder={`Filtrer par ${column.title.toLowerCase()}`} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Tous</SelectItem>
                    {column.options.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            )
          )}
        {isFiltered && (
          <Button
            variant="ghost"
            onClick={() => table.resetColumnFilters()}
            className="h-8 px-2 lg:px-3"
          >
            Réinitialiser
            <X className="ml-2 h-4 w-4" />
          </Button>
        )}
      </div>
      <DataTableViewOptions table={table} />
    </div>
  )
}
