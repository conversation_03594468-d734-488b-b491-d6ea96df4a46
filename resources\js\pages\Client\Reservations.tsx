import { Head } from '@inertiajs/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import ClientLayout from '@/layouts/client-layout';
import { Reservation } from '@/types';
import { format } from 'date-fns';
import { fr } from 'date-fns/locale';
import { Badge } from '@/components/ui/badge';

interface ReservationsProps {
  reservations: Reservation[];
}

export default function Reservations({ reservations = [] }: ReservationsProps) {
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'confirmée':
        return <Badge className="bg-green-500">Confirmée</Badge>;
      case 'en attente':
        return <Badge className="bg-yellow-500">En attente</Badge>;
      case 'annulée':
        return <Badge className="bg-red-500">Annulée</Badge>;
      default:
        return <Badge>{status}</Badge>;
    }
  };

  return (
    <ClientLayout title="Mes réservations">
      <Head title="Mes réservations" />

      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-bold tracking-tight">Mes réservations</h1>
        </div>

        {reservations.length === 0 ? (
          <Card>
            <CardContent className="py-10 text-center">
              <p className="text-muted-foreground">Vous n'avez pas encore de réservations.</p>
            </CardContent>
          </Card>
        ) : (
          <div className="space-y-4">
            {reservations.map((reservation) => (
              <Card key={reservation.id}>
                <CardHeader className="pb-2">
                  <div className="flex items-center justify-between">
                    <CardTitle>
                      Stage du {reservation.stage ? format(new Date(reservation.stage?.date_debut), 'dd MMMM yyyy', { locale: fr }) : ''}
                    </CardTitle>
                    {getStatusBadge(reservation.statut)}
                  </div>
                  <CardDescription>
                    {reservation.stage?.lieu?.nom} - {reservation.stage?.lieu?.ville?.nom}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid gap-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Type de stage</span>
                      <span>{reservation.type_stage?.nom}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Date de réservation</span>
                      <span>{format(new Date(reservation.date_reservation), 'dd/MM/yyyy', { locale: fr })}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Prix</span>
                      <span>{reservation.stage?.prix} €</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>
    </ClientLayout>
  );
}
