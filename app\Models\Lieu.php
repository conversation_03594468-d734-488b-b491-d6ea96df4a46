<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Lieu extends Model
{
    use HasFactory;

    protected $fillable = ['nom', 'adresse', 'ville_id'];

    public function ville()
    {
        return $this->belongsTo(Ville::class);
    }

    public function stages()
    {
        return $this->hasMany(Stage::class);
    }

    public function testPsychos()
    {
        return $this->hasMany(TestPsycho::class);
    }
}
